const { PrismaClient } = require('@prisma/client');

async function checkDatabaseStatus() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 检查数据库连接状态...');
    
    // 测试基本连接
    await prisma.$connect();
    console.log('✅ 数据库连接成功');
    
    // 检查是否存在 Role 表
    try {
      const roleCount = await prisma.role.count();
      console.log(`✅ Role 表存在，包含 ${roleCount} 条记录`);
    } catch (error) {
      console.log('❌ Role 表不存在或无法访问');
      console.log('错误详情:', error.message);
    }
    
    // 检查其他关键表
    const tables = ['user', 'employee', 'permission'];
    for (const table of tables) {
      try {
        const count = await prisma[table].count();
        console.log(`✅ ${table} 表存在，包含 ${count} 条记录`);
      } catch (error) {
        console.log(`❌ ${table} 表不存在或无法访问: ${error.message}`);
      }
    }
    
    // 尝试执行原始 SQL 查询来列出所有表
    try {
      const tables = await prisma.$queryRaw`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        ORDER BY table_name;
      `;
      console.log('\n📋 数据库中的表:');
      tables.forEach(table => {
        console.log(`  - ${table.table_name}`);
      });
    } catch (error) {
      console.log('❌ 无法列出数据库表:', error.message);
    }
    
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    console.error('请检查:');
    console.error('1. PostgreSQL 服务是否运行');
    console.error('2. 数据库连接配置是否正确');
    console.error('3. 数据库是否存在');
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabaseStatus().catch(console.error);
