-- Add performance indexes for critical business operations
-- Migration: 20250628_add_performance_indexes

-- Product model indexes for search and filtering
CREATE INDEX IF NOT EXISTS "Product_name_idx" ON "Product"("name");
CREATE INDEX IF NOT EXISTS "Product_sku_idx" ON "Product"("sku");
CREATE INDEX IF NOT EXISTS "Product_price_idx" ON "Product"("price");
CREATE INDEX IF NOT EXISTS "Product_categoryId_idx" ON "Product"("categoryId");
CREATE INDEX IF NOT EXISTS "Product_material_idx" ON "Product"("material");
CREATE INDEX IF NOT EXISTS "Product_type_idx" ON "Product"("type");
CREATE INDEX IF NOT EXISTS "Product_name_categoryId_idx" ON "Product"("name", "categoryId");
CREATE INDEX IF NOT EXISTS "Product_price_categoryId_idx" ON "Product"("price", "categoryId");

-- Employee model indexes for management operations
CREATE INDEX IF NOT EXISTS "Employee_name_idx" ON "Employee"("name");
CREATE INDEX IF NOT EXISTS "Employee_position_idx" ON "Employee"("position");
CREATE INDEX IF NOT EXISTS "Employee_status_idx" ON "Employee"("status");
CREATE INDEX IF NOT EXISTS "Employee_email_idx" ON "Employee"("email");

-- InventoryItem model indexes for stock management
CREATE INDEX IF NOT EXISTS "InventoryItem_productId_idx" ON "InventoryItem"("productId");
CREATE INDEX IF NOT EXISTS "InventoryItem_warehouseId_idx" ON "InventoryItem"("warehouseId");
CREATE INDEX IF NOT EXISTS "InventoryItem_quantity_idx" ON "InventoryItem"("quantity");
CREATE INDEX IF NOT EXISTS "InventoryItem_warehouse_product_idx" ON "InventoryItem"("warehouseId", "productId");

-- InventoryTransaction model indexes for tracking
CREATE INDEX IF NOT EXISTS "InventoryTransaction_productId_idx" ON "InventoryTransaction"("productId");
CREATE INDEX IF NOT EXISTS "InventoryTransaction_type_idx" ON "InventoryTransaction"("type");
CREATE INDEX IF NOT EXISTS "InventoryTransaction_createdAt_idx" ON "InventoryTransaction"("createdAt");
CREATE INDEX IF NOT EXISTS "InventoryTransaction_referenceType_referenceId_idx" ON "InventoryTransaction"("referenceType", "referenceId");

-- Order model indexes for sales operations
CREATE INDEX IF NOT EXISTS "Order_customerId_idx" ON "Order"("customerId");
CREATE INDEX IF NOT EXISTS "Order_employeeId_idx" ON "Order"("employeeId");
CREATE INDEX IF NOT EXISTS "Order_orderDate_idx" ON "Order"("orderDate");
CREATE INDEX IF NOT EXISTS "Order_status_idx" ON "Order"("status");
CREATE INDEX IF NOT EXISTS "Order_paymentStatus_idx" ON "Order"("paymentStatus");
CREATE INDEX IF NOT EXISTS "Order_orderDate_status_idx" ON "Order"("orderDate", "status");
CREATE INDEX IF NOT EXISTS "Order_customerId_orderDate_idx" ON "Order"("customerId", "orderDate");

-- OrderItem model indexes for order details
CREATE INDEX IF NOT EXISTS "OrderItem_orderId_idx" ON "OrderItem"("orderId");
CREATE INDEX IF NOT EXISTS "OrderItem_productId_idx" ON "OrderItem"("productId");

-- Schedule model indexes for employee scheduling
CREATE INDEX IF NOT EXISTS "Schedule_employeeId_idx" ON "Schedule"("employeeId");
CREATE INDEX IF NOT EXISTS "Schedule_date_idx" ON "Schedule"("date");
CREATE INDEX IF NOT EXISTS "Schedule_employeeId_date_idx" ON "Schedule"("employeeId", "date");

-- GallerySale model indexes for sales tracking
CREATE INDEX IF NOT EXISTS "GallerySale_employeeId_idx" ON "GallerySale"("employeeId");
CREATE INDEX IF NOT EXISTS "GallerySale_date_idx" ON "GallerySale"("date");
CREATE INDEX IF NOT EXISTS "GallerySale_date_employeeId_idx" ON "GallerySale"("date", "employeeId");

-- Workshop model indexes for workshop management
CREATE INDEX IF NOT EXISTS "Workshop_teacherId_idx" ON "Workshop"("teacherId");
CREATE INDEX IF NOT EXISTS "Workshop_date_idx" ON "Workshop"("date");
CREATE INDEX IF NOT EXISTS "Workshop_status_idx" ON "Workshop"("status");
CREATE INDEX IF NOT EXISTS "Workshop_customerId_idx" ON "Workshop"("customerId");
CREATE INDEX IF NOT EXISTS "Workshop_date_status_idx" ON "Workshop"("date", "status");

-- Customer model indexes for customer management
CREATE INDEX IF NOT EXISTS "Customer_name_idx" ON "Customer"("name");
CREATE INDEX IF NOT EXISTS "Customer_phone_idx" ON "Customer"("phone");
CREATE INDEX IF NOT EXISTS "Customer_email_idx" ON "Customer"("email");
CREATE INDEX IF NOT EXISTS "Customer_type_idx" ON "Customer"("type");
CREATE INDEX IF NOT EXISTS "Customer_isActive_idx" ON "Customer"("isActive");

-- Supplier model indexes for purchasing
CREATE INDEX IF NOT EXISTS "Supplier_name_idx" ON "Supplier"("name");
CREATE INDEX IF NOT EXISTS "Supplier_supplierType_idx" ON "Supplier"("supplierType");
CREATE INDEX IF NOT EXISTS "Supplier_isActive_idx" ON "Supplier"("isActive");

-- PurchaseOrder model indexes for procurement
CREATE INDEX IF NOT EXISTS "PurchaseOrder_supplierId_idx" ON "PurchaseOrder"("supplierId");
CREATE INDEX IF NOT EXISTS "PurchaseOrder_employeeId_idx" ON "PurchaseOrder"("employeeId");
CREATE INDEX IF NOT EXISTS "PurchaseOrder_orderDate_idx" ON "PurchaseOrder"("orderDate");
CREATE INDEX IF NOT EXISTS "PurchaseOrder_status_idx" ON "PurchaseOrder"("status");
CREATE INDEX IF NOT EXISTS "PurchaseOrder_approvalStatus_idx" ON "PurchaseOrder"("approvalStatus");

-- ProductionOrder model indexes for production management
CREATE INDEX IF NOT EXISTS "ProductionOrder_productionBaseId_idx" ON "ProductionOrder"("productionBaseId");
CREATE INDEX IF NOT EXISTS "ProductionOrder_employeeId_idx" ON "ProductionOrder"("employeeId");
CREATE INDEX IF NOT EXISTS "ProductionOrder_orderDate_idx" ON "ProductionOrder"("orderDate");
CREATE INDEX IF NOT EXISTS "ProductionOrder_status_idx" ON "ProductionOrder"("status");
CREATE INDEX IF NOT EXISTS "ProductionOrder_priority_idx" ON "ProductionOrder"("priority");

-- Channel model indexes for channel management
CREATE INDEX IF NOT EXISTS "Channel_isActive_idx" ON "Channel"("isActive");
CREATE INDEX IF NOT EXISTS "Channel_status_idx" ON "Channel"("status");

-- ChannelSale model indexes for channel sales
CREATE INDEX IF NOT EXISTS "ChannelSale_channelId_idx" ON "ChannelSale"("channelId");
CREATE INDEX IF NOT EXISTS "ChannelSale_saleDate_idx" ON "ChannelSale"("saleDate");
CREATE INDEX IF NOT EXISTS "ChannelSale_status_idx" ON "ChannelSale"("status");
CREATE INDEX IF NOT EXISTS "ChannelSale_channelId_saleDate_idx" ON "ChannelSale"("channelId", "saleDate");

-- SalaryRecord model indexes for payroll
CREATE INDEX IF NOT EXISTS "SalaryRecord_employeeId_idx" ON "SalaryRecord"("employeeId");
CREATE INDEX IF NOT EXISTS "SalaryRecord_year_month_idx" ON "SalaryRecord"("year", "month");
CREATE INDEX IF NOT EXISTS "SalaryRecord_status_idx" ON "SalaryRecord"("status");

-- PieceWork model indexes for piece work tracking
CREATE INDEX IF NOT EXISTS "PieceWork_employeeId_idx" ON "PieceWork"("employeeId");
CREATE INDEX IF NOT EXISTS "PieceWork_date_idx" ON "PieceWork"("date");
CREATE INDEX IF NOT EXISTS "PieceWork_workType_idx" ON "PieceWork"("workType");

-- CoffeeShopSale model indexes for coffee shop operations
CREATE INDEX IF NOT EXISTS "CoffeeShopSale_date_idx" ON "CoffeeShopSale"("date");
CREATE INDEX IF NOT EXISTS "CoffeeShopSale_employeeId_idx" ON "CoffeeShopSale"("employeeId");

-- User model additional indexes for authentication and user management
CREATE INDEX IF NOT EXISTS "User_role_idx" ON "User"("role");
CREATE INDEX IF NOT EXISTS "User_lastLogin_idx" ON "User"("lastLogin");
CREATE INDEX IF NOT EXISTS "User_createdAt_idx" ON "User"("createdAt");

-- Notification model indexes for notification system
CREATE INDEX IF NOT EXISTS "Notification_userId_idx" ON "Notification"("userId");
CREATE INDEX IF NOT EXISTS "Notification_read_idx" ON "Notification"("read");
CREATE INDEX IF NOT EXISTS "Notification_type_idx" ON "Notification"("type");
CREATE INDEX IF NOT EXISTS "Notification_createdAt_idx" ON "Notification"("createdAt");

-- Add unique constraint for InventoryItem to prevent duplicate warehouse-product combinations
ALTER TABLE "InventoryItem" ADD CONSTRAINT "InventoryItem_warehouseId_productId_unique" UNIQUE ("warehouseId", "productId");

-- Comment to track this migration
COMMENT ON TABLE "Product" IS 'Added performance indexes for search, filtering, and categorization';
COMMENT ON TABLE "InventoryItem" IS 'Added indexes and unique constraint for warehouse-product inventory tracking';
COMMENT ON TABLE "Order" IS 'Added indexes for order management and reporting';
COMMENT ON TABLE "Employee" IS 'Added indexes for employee management and search';