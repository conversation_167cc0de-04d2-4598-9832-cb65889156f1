#!/usr/bin/env node

const { execSync } = require('child_process');
const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

console.log('🔧 数据库 Role 表修复工具');
console.log('================================\n');

async function checkDatabaseConnection() {
  console.log('1️⃣ 检查数据库连接...');
  const prisma = new PrismaClient();
  
  try {
    await prisma.$connect();
    console.log('✅ 数据库连接成功');
    await prisma.$disconnect();
    return true;
  } catch (error) {
    console.log('❌ 数据库连接失败:', error.message);
    return false;
  }
}

async function checkRoleTable() {
  console.log('\n2️⃣ 检查 Role 表状态...');
  const prisma = new PrismaClient();
  
  try {
    await prisma.$connect();
    const roleCount = await prisma.role.count();
    console.log(`✅ Role 表存在，包含 ${roleCount} 条记录`);
    await prisma.$disconnect();
    return true;
  } catch (error) {
    console.log('❌ Role 表不存在或无法访问');
    await prisma.$disconnect();
    return false;
  }
}

function runCommand(command, description) {
  console.log(`\n🔄 ${description}...`);
  console.log(`执行命令: ${command}`);
  
  try {
    const output = execSync(command, { 
      encoding: 'utf8', 
      stdio: 'pipe',
      cwd: process.cwd()
    });
    console.log('✅ 命令执行成功');
    if (output.trim()) {
      console.log('输出:', output.trim());
    }
    return true;
  } catch (error) {
    console.log('❌ 命令执行失败:', error.message);
    if (error.stdout) {
      console.log('标准输出:', error.stdout.toString());
    }
    if (error.stderr) {
      console.log('错误输出:', error.stderr.toString());
    }
    return false;
  }
}

function createBackup() {
  console.log('\n3️⃣ 创建数据库备份...');
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
  const backupFile = `backup_${timestamp}.sql`;
  
  try {
    const backupCommand = `pg_dump postgresql://postgres:postgres@localhost:5432/linghua_enamel_gallery > ${backupFile}`;
    execSync(backupCommand, { encoding: 'utf8' });
    console.log(`✅ 备份创建成功: ${backupFile}`);
    return backupFile;
  } catch (error) {
    console.log('⚠️ 备份创建失败，但继续执行修复:', error.message);
    return null;
  }
}

async function fixDatabase() {
  console.log('\n4️⃣ 修复数据库结构...');
  
  // 方法 1: 尝试应用迁移
  console.log('\n📋 尝试方法 1: 应用数据库迁移');
  if (runCommand('npx prisma migrate deploy', '应用数据库迁移')) {
    if (runCommand('npx prisma generate', '重新生成 Prisma 客户端')) {
      return 'migrate';
    }
  }
  
  // 方法 2: 尝试推送 schema
  console.log('\n📋 尝试方法 2: 推送数据库 Schema');
  if (runCommand('npx prisma db push', '推送数据库 Schema')) {
    if (runCommand('npx prisma generate', '重新生成 Prisma 客户端')) {
      return 'push';
    }
  }
  
  // 方法 3: 强制重置（仅开发环境）
  if (process.env.NODE_ENV !== 'production') {
    console.log('\n📋 尝试方法 3: 强制重置数据库（开发环境）');
    console.log('⚠️ 警告：这将删除所有现有数据！');
    
    // 在实际环境中，这里应该有用户确认
    if (runCommand('npx prisma migrate reset --force', '重置数据库')) {
      return 'reset';
    }
  }
  
  return null;
}

async function verifyFix() {
  console.log('\n5️⃣ 验证修复结果...');
  
  // 检查 Role 表
  const roleExists = await checkRoleTable();
  if (!roleExists) {
    console.log('❌ Role 表仍然不存在');
    return false;
  }
  
  // 检查其他关键表
  const prisma = new PrismaClient();
  const tables = ['permission', 'userRole', 'rolePermission'];
  
  try {
    await prisma.$connect();
    
    for (const table of tables) {
      try {
        const count = await prisma[table].count();
        console.log(`✅ ${table} 表存在，包含 ${count} 条记录`);
      } catch (error) {
        console.log(`❌ ${table} 表检查失败:`, error.message);
        return false;
      }
    }
    
    await prisma.$disconnect();
    return true;
  } catch (error) {
    console.log('❌ 验证过程中发生错误:', error.message);
    return false;
  }
}

async function testApplication() {
  console.log('\n6️⃣ 测试应用初始化...');
  
  try {
    // 导入并测试账户系统初始化
    const { initAccountSystem } = require('./lib/init-account-system');
    await initAccountSystem();
    console.log('✅ 账户管理系统初始化成功');
    return true;
  } catch (error) {
    console.log('❌ 账户管理系统初始化失败:', error.message);
    return false;
  }
}

async function main() {
  try {
    // 检查数据库连接
    const connected = await checkDatabaseConnection();
    if (!connected) {
      console.log('\n❌ 无法连接到数据库，请检查：');
      console.log('1. PostgreSQL 服务是否运行');
      console.log('2. 数据库连接配置是否正确');
      console.log('3. 数据库是否存在');
      process.exit(1);
    }
    
    // 检查 Role 表是否存在
    const roleExists = await checkRoleTable();
    if (roleExists) {
      console.log('\n✅ Role 表已存在，无需修复');
      process.exit(0);
    }
    
    // 创建备份
    const backupFile = createBackup();
    
    // 修复数据库
    const fixMethod = await fixDatabase();
    if (!fixMethod) {
      console.log('\n❌ 所有修复方法都失败了');
      console.log('请手动检查数据库状态或联系技术支持');
      process.exit(1);
    }
    
    console.log(`\n✅ 使用方法 "${fixMethod}" 修复成功`);
    
    // 验证修复
    const verified = await verifyFix();
    if (!verified) {
      console.log('\n❌ 修复验证失败');
      process.exit(1);
    }
    
    // 测试应用
    const appWorking = await testApplication();
    if (!appWorking) {
      console.log('\n⚠️ 数据库修复成功，但应用初始化仍有问题');
      console.log('请检查应用日志获取更多信息');
      process.exit(2);
    }
    
    console.log('\n🎉 修复完成！');
    console.log('================================');
    console.log('✅ 数据库连接正常');
    console.log('✅ Role 表及相关表已创建');
    console.log('✅ 账户管理系统初始化成功');
    console.log('✅ 应用可以正常启动');
    
    if (backupFile) {
      console.log(`\n💾 数据库备份文件: ${backupFile}`);
    }
    
    console.log('\n🚀 现在可以启动应用: npm run dev');
    
  } catch (error) {
    console.error('\n💥 修复过程中发生未预期的错误:', error);
    process.exit(1);
  }
}

// 运行修复程序
main();
