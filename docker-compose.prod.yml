# 聆花珐琅馆ERP系统 - Docker Compose生产环境
version: '3.8'

services:
  # PostgreSQL数据库服务
  db:
    image: postgres:15-alpine
    container_name: linghua-erp-db-prod
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      POSTGRES_DB: linghua_enamel_gallery
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
    networks:
      - linghua-network-prod
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d linghua_enamel_gallery"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Next.js应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: linghua-erp-app-prod
    restart: always
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-postgres}@db:5432/linghua_enamel_gallery?schema=public
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - NEXTAUTH_URL=${NEXTAUTH_URL}
      - AUTH_TRUST_HOST=true
      - AUTH_SECRET=${AUTH_SECRET}
    ports:
      - "3000:3000"
    networks:
      - linghua-network-prod
    depends_on:
      db:
        condition: service_healthy

# 网络配置
networks:
  linghua-network-prod:
    driver: bridge

# 数据卷配置
volumes:
  postgres_data_prod:
    driver: local