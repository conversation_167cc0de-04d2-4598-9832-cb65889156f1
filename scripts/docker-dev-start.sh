#!/bin/bash

# 聆花珐琅馆ERP系统 - Docker开发环境启动脚本

set -e

echo "🚀 启动聆花珐琅馆ERP Docker开发环境..."

# 检查Docker是否运行
if ! docker info >/dev/null 2>&1; then
    echo "❌ 错误: Docker未运行，请先启动Docker"
    exit 1
fi

# 停止现有的独立容器（如果存在）
echo "🛑 停止现有的独立容器..."
docker stop 0606erp 2>/dev/null || true

# 停止本地PostgreSQL服务
echo "🛑 停止本地PostgreSQL服务..."
brew services stop postgresql@14 2>/dev/null || true
brew services stop postgresql@15 2>/dev/null || true

# 清理旧的容器和网络
echo "🧹 清理旧的容器和网络..."
docker-compose down 2>/dev/null || true

# 构建并启动服务
echo "🔨 构建Docker镜像..."
docker-compose build --no-cache

echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 检查数据库连接
echo "🔍 检查数据库连接..."
if docker-compose exec -T db pg_isready -U postgres -d linghua_enamel_gallery >/dev/null 2>&1; then
    echo "✅ 数据库连接正常"
else
    echo "❌ 数据库连接失败"
    docker-compose logs db
    exit 1
fi

# 检查应用状态
echo "🔍 检查应用状态..."
sleep 10
if curl -f http://localhost:3001 >/dev/null 2>&1; then
    echo "✅ 应用启动成功"
else
    echo "⏳ 应用仍在启动中，请稍等..."
fi

echo ""
echo "🎉 Docker开发环境启动完成!"
echo ""
echo "📋 服务信息:"
echo "   应用地址: http://localhost:3001"
echo "   数据库地址: localhost:5432"
echo "   数据库名: linghua_enamel_gallery"
echo "   用户名: postgres"
echo "   密码: postgres"
echo ""
echo "👤 管理员账户:"
echo "   邮箱: <EMAIL>"
echo "   密码: Admin123456"
echo ""
echo "🔧 常用命令:"
echo "   查看日志: docker-compose logs -f"
echo "   停止服务: docker-compose down"
echo "   重启服务: docker-compose restart"
echo "   进入应用容器: docker-compose exec app sh"
echo "   进入数据库: docker-compose exec db psql -U postgres -d linghua_enamel_gallery"
echo ""