# 🚀 性能优化环境变量配置
# 解决日志中发现的性能问题

# NextAuth 优化配置
NEXTAUTH_DEBUG=false
NEXTAUTH_SECRET=your-secret-key-here

# 数据库连接优化
DATABASE_URL="postgresql://username:password@localhost:5432/database?connection_limit=10&pool_timeout=10&connect_timeout=10"

# Node.js 性能优化
NODE_OPTIONS="--max-old-space-size=4096 --optimize-for-size"
NODE_ENV=production

# 日志级别优化
LOG_LEVEL=warn
NEXT_LOG_LEVEL=warn

# 编译优化
NEXT_TELEMETRY_DISABLED=1
ANALYZE=false

# 缓存配置
REDIS_URL=redis://localhost:6379
ENABLE_CACHE=true
CACHE_TTL=300

# 性能监控
PERFORMANCE_MONITORING=true
SLOW_QUERY_LOG=true
API_TIMEOUT=30000

# 图片优化
NEXT_IMAGES_OPTIMIZE=true
NEXT_IMAGES_FORMATS=webp,avif

# 安全优化
SECURE_COOKIES=true
TRUST_HOST=true