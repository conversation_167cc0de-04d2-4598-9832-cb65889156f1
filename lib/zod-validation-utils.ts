/**
 * Zod验证工具
 * 
 * 将Zod验证与ErrorUtils框架集成，提供统一的验证接口
 * 
 * @module ZodValidationUtils
 * @category 验证模块
 */

import { z } from "zod";
import { ErrorUtils } from "@/lib/error-utils";

/**
 * Zod验证结果类型
 */
export type ValidationResult<T> = {
  success: true;
  data: T;
} | {
  success: false;
  error: ErrorUtils.ValidationError;
};

/**
 * Zod验证工具类
 */
export class ZodValidationUtils {
  
  /**
   * 验证数据并转换Zod错误为ErrorUtils格式
   * 
   * @param schema Zod验证schema
   * @param data 待验证的数据
   * @param module 模块名称
   * @returns 验证结果
   */
  static validate<T>(
    schema: z.ZodSchema<T>,
    data: unknown,
    module: string = 'zod-validation'
  ): ValidationResult<T> {
    try {
      const result = schema.parse(data);
      return {
        success: true,
        data: result
      };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const validationError = this.convertZodErrorToValidationError(error, module);
        return {
          success: false,
          error: validationError
        };
      }
      
      // 非Zod错误，重新抛出
      throw error;
    }
  }

  /**
   * 异步验证数据（用于需要数据库查询的验证）
   * 
   * @param schema Zod验证schema
   * @param data 待验证的数据
   * @param module 模块名称
   * @returns 验证结果Promise
   */
  static async validateAsync<T>(
    schema: z.ZodSchema<T>,
    data: unknown,
    module: string = 'zod-validation'
  ): Promise<ValidationResult<T>> {
    try {
      const result = await schema.parseAsync(data);
      return {
        success: true,
        data: result
      };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const validationError = this.convertZodErrorToValidationError(error, module);
        return {
          success: false,
          error: validationError
        };
      }
      
      // 非Zod错误，重新抛出
      throw error;
    }
  }

  /**
   * 验证数据并在失败时抛出ErrorUtils.ValidationError
   * 
   * @param schema Zod验证schema
   * @param data 待验证的数据
   * @param module 模块名称
   * @returns 验证通过的数据
   * @throws ErrorUtils.ValidationError 验证失败时抛出
   */
  static validateOrThrow<T>(
    schema: z.ZodSchema<T>,
    data: unknown,
    module: string = 'zod-validation'
  ): T {
    const result = this.validate(schema, data, module);
    
    if (!result.success) {
      throw result.error;
    }
    
    return result.data;
  }

  /**
   * 异步验证数据并在失败时抛出ErrorUtils.ValidationError
   * 
   * @param schema Zod验证schema
   * @param data 待验证的数据
   * @param module 模块名称
   * @returns 验证通过的数据Promise
   * @throws ErrorUtils.ValidationError 验证失败时抛出
   */
  static async validateOrThrowAsync<T>(
    schema: z.ZodSchema<T>,
    data: unknown,
    module: string = 'zod-validation'
  ): Promise<T> {
    const result = await this.validateAsync(schema, data, module);
    
    if (!result.success) {
      throw result.error;
    }
    
    return result.data;
  }

  /**
   * 将Zod错误转换为ErrorUtils.ValidationError
   * 
   * @param zodError Zod验证错误
   * @param module 模块名称
   * @returns ErrorUtils.ValidationError
   */
  private static convertZodErrorToValidationError(
    zodError: z.ZodError,
    module: string
  ): ErrorUtils.ValidationError {
    // 提取第一个错误作为主要错误信息
    const firstIssue = zodError.issues[0];
    
    // 构建错误消息
    let message = firstIssue.message;
    if (firstIssue.path.length > 0) {
      const fieldPath = firstIssue.path.join('.');
      message = `字段 '${fieldPath}': ${message}`;
    }

    // 构建详细的错误上下文
    const context = {
      zodErrors: zodError.issues.map(issue => ({
        path: issue.path,
        message: issue.message,
        code: issue.code,
        received: 'received' in issue ? issue.received : undefined,
        expected: 'expected' in issue ? issue.expected : undefined,
      })),
      totalErrors: zodError.issues.length,
      firstErrorPath: firstIssue.path,
      firstErrorCode: firstIssue.code,
    };

    return new ErrorUtils.ValidationError(message, context, module);
  }

  /**
   * 获取Zod错误的详细信息（用于调试）
   * 
   * @param zodError Zod验证错误
   * @returns 格式化的错误信息
   */
  static getZodErrorDetails(zodError: z.ZodError): {
    summary: string;
    details: Array<{
      field: string;
      message: string;
      code: string;
      value?: unknown;
    }>;
  } {
    const details = zodError.issues.map(issue => ({
      field: issue.path.join('.') || 'root',
      message: issue.message,
      code: issue.code,
      value: 'received' in issue ? issue.received : undefined,
    }));

    const summary = `验证失败：${details.length}个错误`;

    return { summary, details };
  }

  /**
   * 创建自定义验证函数
   * 
   * @param validator 自定义验证函数
   * @param errorMessage 错误消息
   * @returns Zod refinement函数
   */
  static createCustomValidator<T>(
    validator: (value: T) => boolean | Promise<boolean>,
    errorMessage: string
  ) {
    return (value: T) => {
      const result = validator(value);
      if (result instanceof Promise) {
        return result;
      }
      return result;
    };
  }

  /**
   * 验证请求体数据的中间件函数
   * 
   * @param schema Zod验证schema
   * @param module 模块名称
   * @returns 验证中间件函数
   */
  static createValidationMiddleware<T>(
    schema: z.ZodSchema<T>,
    module: string = 'api-validation'
  ) {
    return async (data: unknown): Promise<T> => {
      return this.validateOrThrowAsync(schema, data, module);
    };
  }

  /**
   * 验证查询参数（将字符串转换为适当的类型）
   * 
   * @param schema Zod验证schema
   * @param queryParams URL查询参数对象
   * @param module 模块名称
   * @returns 验证并转换后的查询参数
   */
  static validateQueryParams<T>(
    schema: z.ZodSchema<T>,
    queryParams: Record<string, string | string[] | undefined>,
    module: string = 'query-validation'
  ): T {
    // 预处理查询参数，转换字符串为适当的类型
    const processedParams = this.preprocessQueryParams(queryParams);
    
    return this.validateOrThrow(schema, processedParams, module);
  }

  /**
   * 预处理查询参数，转换字符串为适当的类型
   * 
   * @param params 原始查询参数
   * @returns 处理后的参数
   */
  private static preprocessQueryParams(
    params: Record<string, string | string[] | undefined>
  ): Record<string, unknown> {
    const processed: Record<string, unknown> = {};

    for (const [key, value] of Object.entries(params)) {
      if (value === undefined || value === '') {
        continue;
      }

      if (Array.isArray(value)) {
        processed[key] = value;
        continue;
      }

      // 尝试转换数字
      if (/^\d+$/.test(value)) {
        processed[key] = parseInt(value, 10);
        continue;
      }

      // 尝试转换浮点数
      if (/^\d+\.\d+$/.test(value)) {
        processed[key] = parseFloat(value);
        continue;
      }

      // 尝试转换布尔值
      if (value === 'true' || value === 'false') {
        processed[key] = value === 'true';
        continue;
      }

      // 保持字符串
      processed[key] = value;
    }

    return processed;
  }
}
