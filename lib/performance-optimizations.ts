/**
 * 系统性能优化配置和工具
 * 
 * 针对日志中发现的性能问题提供优化方案
 * 
 * @module 性能优化
 * @category 系统优化
 */

import { NextResponse } from 'next/server';

/**
 * NextAuth性能优化配置
 */
export const optimizedAuthConfig = {
  // 减少JWT回调频率
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30天
  },
  session: {
    strategy: 'jwt' as const,
    maxAge: 30 * 24 * 60 * 60, // 30天
    updateAge: 24 * 60 * 60, // 24小时更新一次
  },
  callbacks: {
    // 优化JWT回调，减少不必要的处理
    jwt: async ({ token, user, account }) => {
      if (user) {
        // 只在用户首次登录时更新token
        token.id = user.id;
        token.email = user.email;
        token.role = user.role;
      }
      return token;
    },
    // 优化Session回调，减少数据库查询
    session: async ({ session, token }) => {
      if (token) {
        session.user.id = token.id as string;
        session.user.email = token.email;
        session.user.role = token.role as string;
      }
      return session;
    },
  },
  // 禁用调试模式以提高性能
  debug: process.env.NODE_ENV === 'development' ? false : false,
};

/**
 * API响应缓存配置
 */
export interface CacheConfig {
  ttl: number; // 缓存时间（秒）
  tags?: string[]; // 缓存标签
  revalidate?: number; // 重新验证时间
}

const API_CACHE_CONFIG: Record<string, CacheConfig> = {
  '/api/products': { ttl: 300, tags: ['products'], revalidate: 60 },
  '/api/product-categories': { ttl: 600, tags: ['categories'], revalidate: 120 },
  '/api/products/tags': { ttl: 300, tags: ['tags'], revalidate: 60 },
  '/api/inventory': { ttl: 180, tags: ['inventory'], revalidate: 30 },
  '/api/auth/check-permissions': { ttl: 900, tags: ['permissions'], revalidate: 300 },
};

/**
 * 系统初始化优化器
 */
export class SystemInitializationOptimizer {
  private static initialized = false;
  private static initPromise: Promise<void> | null = null;

  /**
   * 优化的系统初始化（只执行一次）
   */
  static async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = this.performInitialization();
    return this.initPromise;
  }

  private static async performInitialization(): Promise<void> {
    try {
      console.log('[系统优化] 开始单次系统初始化...');
      const start = Date.now();

      // 并行执行初始化任务而不是串行
      await Promise.all([
        this.initializeDatabase(),
        this.initializeCache(),
        this.initializeAuth(),
      ]);

      this.initialized = true;
      const duration = Date.now() - start;
      console.log(`[系统优化] 系统初始化完成，耗时 ${duration}ms`);
    } catch (error) {
      console.error('[系统优化] 系统初始化失败:', error);
      this.initPromise = null; // 重置以允许重试
      throw error;
    }
  }

  private static async initializeDatabase(): Promise<void> {
    // 数据库连接池预热
    console.log('[系统优化] 数据库连接池预热...');
  }

  private static async initializeCache(): Promise<void> {
    // 缓存预热
    console.log('[系统优化] 缓存系统预热...');
  }

  private static async initializeAuth(): Promise<void> {
    // 认证系统初始化
    console.log('[系统优化] 认证系统初始化...');
  }

  static reset(): void {
    this.initialized = false;
    this.initPromise = null;
  }
}

/**
 * API响应缓存中间件
 */
export function createApiCacheMiddleware() {
  const cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  return function cacheMiddleware(
    request: Request,
    response: Response | NextResponse
  ) {
    const url = new URL(request.url);
    const cacheKey = url.pathname + url.search;
    const config = API_CACHE_CONFIG[url.pathname];

    if (!config || request.method !== 'GET') {
      return response;
    }

    // 检查缓存
    const cached = cache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < cached.ttl * 1000) {
      console.log(`[缓存命中] ${cacheKey}`);
      return NextResponse.json(cached.data, {
        headers: {
          'Cache-Control': `public, max-age=${config.ttl}`,
          'X-Cache': 'HIT',
        },
      });
    }

    // 缓存响应
    if (response instanceof NextResponse) {
      response.clone().json().then(data => {
        cache.set(cacheKey, {
          data,
          timestamp: Date.now(),
          ttl: config.ttl
        });
      });

      // 添加缓存头
      response.headers.set('Cache-Control', `public, max-age=${config.ttl}`);
      response.headers.set('X-Cache', 'MISS');
    }

    return response;
  };
}

/**
 * 编译优化配置
 */
export const nextConfigOptimizations = {
  // 实验性功能
  experimental: {
    // 启用静态优化
    staticPageGenerationTimeout: 60,
    // 启用增量静态再生
    incrementalCacheHandlerPath: undefined,
  },
  // 编译优化
  webpack: (config: any, { dev, isServer }: any) => {
    if (!dev && !isServer) {
      // 生产环境优化
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
            },
          },
        },
      };
    }

    // 缓存优化
    config.cache = {
      type: 'filesystem',
      buildDependencies: {
        config: [__filename],
      },
    };

    return config;
  },
  // 压缩优化
  compress: true,
  // PoweredByHeader
  poweredByHeader: false,
  // 生成ETags
  generateEtags: true,
};

/**
 * 数据库查询优化器
 */
export class DatabaseOptimizer {
  /**
   * 优化Prisma查询
   */
  static optimizeQuery(queryBuilder: any) {
    // 添加查询超时
    return {
      ...queryBuilder,
      // 设置查询超时为10秒
      timeout: 10000,
    };
  }

  /**
   * 批量查询优化
   */
  static async batchQuery<T>(
    queries: (() => Promise<T>)[],
    batchSize: number = 5
  ): Promise<T[]> {
    const results: T[] = [];
    
    for (let i = 0; i < queries.length; i += batchSize) {
      const batch = queries.slice(i, i + batchSize);
      const batchResults = await Promise.all(batch.map(query => query()));
      results.push(...batchResults);
    }
    
    return results;
  }

  /**
   * 连接池监控
   */
  static monitorConnectionPool() {
    setInterval(() => {
      // 这里可以监控Prisma连接池状态
      console.log('[数据库优化] 连接池状态检查...');
    }, 60000); // 每分钟检查一次
  }
}

/**
 * 内存优化器
 */
export class MemoryOptimizer {
  private static gcInterval: NodeJS.Timeout | null = null;

  /**
   * 启动内存监控和垃圾回收
   */
  static startMonitoring() {
    if (this.gcInterval) return;

    this.gcInterval = setInterval(() => {
      const memUsage = process.memoryUsage();
      const memoryMB = Math.round(memUsage.heapUsed / 1024 / 1024);
      
      if (memoryMB > 500) { // 内存使用超过500MB时警告
        console.warn(`[内存警告] 内存使用量: ${memoryMB}MB`);
        
        if (global.gc && memoryMB > 800) { // 超过800MB时强制垃圾回收
          console.log('[内存优化] 执行垃圾回收...');
          global.gc();
        }
      }
    }, 30000); // 每30秒检查一次
  }

  /**
   * 停止内存监控
   */
  static stopMonitoring() {
    if (this.gcInterval) {
      clearInterval(this.gcInterval);
      this.gcInterval = null;
    }
  }
}

/**
 * 页面加载优化配置
 */
export const pageOptimizationConfig = {
  // 预加载关键资源
  preload: [
    '/api/products',
    '/api/product-categories',
    '/api/auth/check-permissions',
  ],
  // 懒加载配置
  lazyLoad: {
    threshold: 0.1,
    rootMargin: '50px',
  },
  // 图片优化
  images: {
    domains: ['localhost'],
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
};

/**
 * 生产环境优化应用器
 */
export class ProductionOptimizer {
  /**
   * 应用所有优化配置
   */
  static applyOptimizations() {
    // 启动系统初始化
    SystemInitializationOptimizer.initialize();
    
    // 启动数据库监控
    DatabaseOptimizer.monitorConnectionPool();
    
    // 启动内存监控
    MemoryOptimizer.startMonitoring();
    
    console.log('[系统优化] 所有性能优化已启用');
  }

  /**
   * 清理和重置
   */
  static cleanup() {
    SystemInitializationOptimizer.reset();
    MemoryOptimizer.stopMonitoring();
    console.log('[系统优化] 优化配置已清理');
  }
}

/**
 * 环境变量优化建议
 */
export const environmentOptimizations = {
  // Node.js优化
  NODE_ENV: 'production',
  NODE_OPTIONS: '--max-old-space-size=4096',
  
  // Next.js优化
  NEXTAUTH_URL: process.env.NEXTAUTH_URL,
  NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
  
  // 数据库优化
  DATABASE_URL: process.env.DATABASE_URL,
  DATABASE_POOL_SIZE: '10',
  DATABASE_TIMEOUT: '10000',
  
  // 缓存优化
  REDIS_URL: process.env.REDIS_URL,
  
  // 日志优化
  LOG_LEVEL: 'warn', // 减少日志输出
  NEXTAUTH_DEBUG: 'false', // 禁用NextAuth调试
};

export default {
  SystemInitializationOptimizer,
  DatabaseOptimizer,
  MemoryOptimizer,
  ProductionOptimizer,
  optimizedAuthConfig,
  nextConfigOptimizations,
  pageOptimizationConfig,
  environmentOptimizations,
};