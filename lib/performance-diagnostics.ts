/**
 * 系统性能诊断和优化工具
 * 
 * 用于检测和修复常见的性能问题
 * 
 * @module 性能诊断
 * @category 系统优化
 */

export interface PerformanceMetrics {
  timestamp: number;
  requestId: string;
  operation: string;
  duration: number;
  details?: any;
}

export interface DatabaseQueryMetrics {
  query: string;
  duration: number;
  resultCount?: number;
  timestamp: number;
}

export interface CompilationMetrics {
  route: string;
  duration: number;
  moduleCount: number;
  timestamp: number;
}

/**
 * 性能监控器
 */
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetrics[] = [];
  private dbMetrics: DatabaseQueryMetrics[] = [];
  private compilationMetrics: CompilationMetrics[] = [];
  private isEnabled: boolean = false;

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  enable() {
    this.isEnabled = true;
  }

  disable() {
    this.isEnabled = false;
  }

  /**
   * 记录操作性能指标
   */
  recordOperation(operation: string, duration: number, details?: any) {
    if (!this.isEnabled) return;

    const metric: PerformanceMetrics = {
      timestamp: Date.now(),
      requestId: this.generateRequestId(),
      operation,
      duration,
      details
    };

    this.metrics.push(metric);
    
    // 保持最近1000条记录
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }

    // 如果操作耗时超过阈值，记录警告
    if (duration > 1000) {
      console.warn(`⚠️ [性能警告] ${operation} 耗时 ${duration}ms，超过1秒阈值`);
    }
  }

  /**
   * 记录数据库查询性能
   */
  recordDatabaseQuery(query: string, duration: number, resultCount?: number) {
    if (!this.isEnabled) return;

    const metric: DatabaseQueryMetrics = {
      query: this.sanitizeQuery(query),
      duration,
      resultCount,
      timestamp: Date.now()
    };

    this.dbMetrics.push(metric);
    
    if (this.dbMetrics.length > 500) {
      this.dbMetrics = this.dbMetrics.slice(-500);
    }

    // 慢查询警告
    if (duration > 500) {
      console.warn(`🐌 [慢查询] ${metric.query} 耗时 ${duration}ms`);
    }
  }

  /**
   * 记录编译性能
   */
  recordCompilation(route: string, duration: number, moduleCount: number) {
    if (!this.isEnabled) return;

    const metric: CompilationMetrics = {
      route,
      duration,
      moduleCount,
      timestamp: Date.now()
    };

    this.compilationMetrics.push(metric);
    
    if (this.compilationMetrics.length > 100) {
      this.compilationMetrics = this.compilationMetrics.slice(-100);
    }

    // 编译时间警告
    if (duration > 2000) {
      console.warn(`🔥 [编译缓慢] ${route} 编译耗时 ${duration}ms，模块数量 ${moduleCount}`);
    }
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    const now = Date.now();
    const last5Minutes = now - 5 * 60 * 1000;

    // 最近5分钟的指标
    const recentMetrics = this.metrics.filter(m => m.timestamp > last5Minutes);
    const recentDbMetrics = this.dbMetrics.filter(m => m.timestamp > last5Minutes);
    const recentCompilationMetrics = this.compilationMetrics.filter(m => m.timestamp > last5Minutes);

    // 统计分析
    const slowOperations = recentMetrics.filter(m => m.duration > 1000);
    const slowQueries = recentDbMetrics.filter(m => m.duration > 500);
    const slowCompilations = recentCompilationMetrics.filter(m => m.duration > 2000);

    // 平均响应时间
    const avgResponseTime = recentMetrics.length > 0 
      ? recentMetrics.reduce((sum, m) => sum + m.duration, 0) / recentMetrics.length 
      : 0;

    // 平均查询时间
    const avgQueryTime = recentDbMetrics.length > 0
      ? recentDbMetrics.reduce((sum, m) => sum + m.duration, 0) / recentDbMetrics.length
      : 0;

    return {
      period: "最近5分钟",
      summary: {
        totalOperations: recentMetrics.length,
        avgResponseTime: Math.round(avgResponseTime),
        slowOperations: slowOperations.length,
        totalQueries: recentDbMetrics.length,
        avgQueryTime: Math.round(avgQueryTime),
        slowQueries: slowQueries.length,
        compilations: recentCompilationMetrics.length,
        slowCompilations: slowCompilations.length
      },
      details: {
        slowOperations: slowOperations.slice(0, 10),
        slowQueries: slowQueries.slice(0, 10),
        slowCompilations: slowCompilations.slice(0, 5)
      }
    };
  }

  /**
   * 获取优化建议
   */
  getOptimizationSuggestions() {
    const report = this.getPerformanceReport();
    const suggestions: string[] = [];

    // 响应时间优化建议
    if (report.summary.avgResponseTime > 1000) {
      suggestions.push("平均响应时间过长，建议检查API实现和数据库查询效率");
    }

    // 数据库优化建议
    if (report.summary.avgQueryTime > 300) {
      suggestions.push("数据库查询较慢，建议添加索引或优化查询语句");
    }

    // 编译优化建议
    if (report.summary.slowCompilations > 0) {
      suggestions.push("编译时间过长，建议启用缓存和代码分割");
    }

    // 慢操作建议
    if (report.summary.slowOperations > 5) {
      suggestions.push("存在多个慢操作，建议启用缓存和异步处理");
    }

    return suggestions;
  }

  private generateRequestId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  private sanitizeQuery(query: string): string {
    // 移除敏感信息，只保留查询结构
    return query.substring(0, 100) + (query.length > 100 ? '...' : '');
  }

  /**
   * 清理旧数据
   */
  cleanup() {
    const oneHourAgo = Date.now() - 60 * 60 * 1000;
    this.metrics = this.metrics.filter(m => m.timestamp > oneHourAgo);
    this.dbMetrics = this.dbMetrics.filter(m => m.timestamp > oneHourAgo);
    this.compilationMetrics = this.compilationMetrics.filter(m => m.timestamp > oneHourAgo);
  }
}

/**
 * 性能优化中间件
 */
export function createPerformanceMiddleware() {
  const monitor = PerformanceMonitor.getInstance();
  monitor.enable();

  return (operation: string) => {
    const start = Date.now();
    
    return {
      end: (details?: any) => {
        const duration = Date.now() - start;
        monitor.recordOperation(operation, duration, details);
        return duration;
      }
    };
  };
}

/**
 * 数据库查询包装器
 */
export function wrapDatabaseQuery<T>(
  queryName: string,
  queryFn: () => Promise<T>
): Promise<T> {
  const monitor = PerformanceMonitor.getInstance();
  const start = Date.now();

  return queryFn().then(result => {
    const duration = Date.now() - start;
    const resultCount = Array.isArray(result) ? result.length : 1;
    monitor.recordDatabaseQuery(queryName, duration, resultCount);
    return result;
  }).catch(error => {
    const duration = Date.now() - start;
    monitor.recordDatabaseQuery(`${queryName} (ERROR)`, duration, 0);
    throw error;
  });
}

/**
 * API路由性能包装器
 */
export function withPerformanceTracking<T extends (...args: any[]) => any>(
  routeName: string,
  handler: T
): T {
  const monitor = PerformanceMonitor.getInstance();

  return ((...args: any[]) => {
    const start = Date.now();
    const result = handler(...args);

    if (result && typeof result.then === 'function') {
      // 异步处理
      return result.then((response: any) => {
        const duration = Date.now() - start;
        monitor.recordOperation(`API:${routeName}`, duration, {
          status: response?.status || 200
        });
        return response;
      }).catch((error: any) => {
        const duration = Date.now() - start;
        monitor.recordOperation(`API:${routeName} (ERROR)`, duration, {
          error: error.message
        });
        throw error;
      });
    } else {
      // 同步处理
      const duration = Date.now() - start;
      monitor.recordOperation(`API:${routeName}`, duration);
      return result;
    }
  }) as T;
}

/**
 * 获取系统性能状态
 */
export function getSystemPerformanceStatus() {
  const monitor = PerformanceMonitor.getInstance();
  const report = monitor.getPerformanceReport();
  const suggestions = monitor.getOptimizationSuggestions();

  return {
    status: report.summary.avgResponseTime < 500 ? 'good' : 
            report.summary.avgResponseTime < 1000 ? 'warning' : 'critical',
    report,
    suggestions,
    timestamp: new Date().toISOString()
  };
}