/**
 * 产品和价格验证工具
 * 
 * 提供产品分类验证和价格计算验证功能
 * 
 * @module ValidationUtils
 * @category 核心模块
 */

import prisma from "@/lib/db";
import { ErrorUtils } from "@/lib/error-utils";

/**
 * 产品验证选项
 */
export interface ProductValidationOptions {
  checkCategory?: boolean;
  checkPrice?: boolean;
  checkCommissionRate?: boolean;
  allowZeroPrice?: boolean;
  maxPrice?: number;
  maxCommissionRate?: number;
}

/**
 * 价格验证选项
 */
export interface PriceValidationOptions {
  allowZero?: boolean;
  maxValue?: number;
  decimalPlaces?: number;
  currency?: string;
}

/**
 * 产品验证工具类
 */
export class ProductValidationUtils {
  
  /**
   * 验证产品分类ID
   * 
   * @param categoryId 分类ID
   * @param module 模块名称
   * @returns 验证结果
   */
  static async validateCategoryId(
    categoryId: number | null | undefined,
    module: string = 'product-validation'
  ): Promise<void> {
    // 如果分类ID为空，跳过验证（允许无分类产品）
    if (!categoryId) {
      return;
    }

    // 检查分类是否存在
    const category = await prisma.productCategory.findUnique({
      where: { id: categoryId },
      select: { id: true, name: true, isActive: true }
    });

    if (!category) {
      throw new ErrorUtils.ValidationError(
        "指定的产品分类不存在",
        { categoryId },
        module
      );
    }

    // 检查分类是否激活
    if (!category.isActive) {
      throw new ErrorUtils.BusinessLogicError(
        "指定的产品分类已被禁用",
        { categoryId, categoryName: category.name },
        module
      );
    }
  }

  /**
   * 验证产品基本信息
   * 
   * @param productData 产品数据
   * @param options 验证选项
   * @param module 模块名称
   */
  static async validateProductData(
    productData: {
      name?: string;
      categoryId?: number | null;
      price?: number | null;
      commissionRate?: number | null;
    },
    options: ProductValidationOptions = {},
    module: string = 'product-validation'
  ): Promise<void> {
    const {
      checkCategory = true,
      checkPrice = true,
      checkCommissionRate = true,
      allowZeroPrice = true,
      maxPrice = 999999.99,
      maxCommissionRate = 100
    } = options;

    // 验证产品名称
    if (!productData.name || typeof productData.name !== 'string' || productData.name.trim() === '') {
      throw new ErrorUtils.ValidationError(
        "产品名称为必填项",
        { field: 'name' },
        module
      );
    }

    // 验证产品名称长度
    if (productData.name.trim().length > 255) {
      throw new ErrorUtils.ValidationError(
        "产品名称不能超过255个字符",
        { field: 'name', length: productData.name.trim().length },
        module
      );
    }

    // 验证分类ID
    if (checkCategory) {
      await this.validateCategoryId(productData.categoryId, module);
    }

    // 验证价格
    if (checkPrice && productData.price !== null && productData.price !== undefined) {
      await this.validatePrice(productData.price, {
        allowZero: allowZeroPrice,
        maxValue: maxPrice,
        decimalPlaces: 2
      }, module);
    }

    // 验证佣金率
    if (checkCommissionRate && productData.commissionRate !== null && productData.commissionRate !== undefined) {
      await this.validateCommissionRate(productData.commissionRate, maxCommissionRate, module);
    }
  }

  /**
   * 验证价格
   * 
   * @param price 价格
   * @param options 验证选项
   * @param module 模块名称
   */
  static async validatePrice(
    price: number,
    options: PriceValidationOptions = {},
    module: string = 'price-validation'
  ): Promise<void> {
    const {
      allowZero = true,
      maxValue = 999999.99,
      decimalPlaces = 2
    } = options;

    // 检查是否为有效数字
    if (typeof price !== 'number' || isNaN(price)) {
      throw new ErrorUtils.ValidationError(
        "价格必须是有效的数字",
        { price },
        module
      );
    }

    // 检查是否为负数
    if (price < 0) {
      throw new ErrorUtils.ValidationError(
        "价格不能为负数",
        { price },
        module
      );
    }

    // 检查零值
    if (!allowZero && price === 0) {
      throw new ErrorUtils.ValidationError(
        "价格不能为零",
        { price },
        module
      );
    }

    // 检查最大值
    if (price > maxValue) {
      throw new ErrorUtils.ValidationError(
        `价格不能超过 ${maxValue}`,
        { price, maxValue },
        module
      );
    }

    // 检查小数位数
    const decimalCount = (price.toString().split('.')[1] || '').length;
    if (decimalCount > decimalPlaces) {
      throw new ErrorUtils.ValidationError(
        `价格小数位数不能超过 ${decimalPlaces} 位`,
        { price, decimalCount, maxDecimalPlaces: decimalPlaces },
        module
      );
    }
  }

  /**
   * 验证佣金率
   * 
   * @param commissionRate 佣金率
   * @param maxRate 最大佣金率
   * @param module 模块名称
   */
  static async validateCommissionRate(
    commissionRate: number,
    maxRate: number = 100,
    module: string = 'commission-validation'
  ): Promise<void> {
    // 检查是否为有效数字
    if (typeof commissionRate !== 'number' || isNaN(commissionRate)) {
      throw new ErrorUtils.ValidationError(
        "佣金率必须是有效的数字",
        { commissionRate },
        module
      );
    }

    // 检查是否为负数
    if (commissionRate < 0) {
      throw new ErrorUtils.ValidationError(
        "佣金率不能为负数",
        { commissionRate },
        module
      );
    }

    // 检查最大值
    if (commissionRate > maxRate) {
      throw new ErrorUtils.ValidationError(
        `佣金率不能超过 ${maxRate}%`,
        { commissionRate, maxRate },
        module
      );
    }
  }
}

/**
 * 订单价格验证工具类
 */
export class OrderPriceValidationUtils {
  
  /**
   * 验证订单项价格
   * 
   * @param orderItems 订单项列表
   * @param module 模块名称
   */
  static async validateOrderItemPrices(
    orderItems: Array<{
      productId: number;
      quantity: number;
      price: number;
      discount?: number;
    }>,
    module: string = 'order-validation'
  ): Promise<void> {
    for (const [index, item] of orderItems.entries()) {
      // 验证数量
      if (!Number.isInteger(item.quantity) || item.quantity <= 0) {
        throw new ErrorUtils.ValidationError(
          `订单项 ${index + 1} 的数量必须是正整数`,
          { index, quantity: item.quantity },
          module
        );
      }

      // 验证价格
      await ProductValidationUtils.validatePrice(
        item.price,
        { allowZero: false, maxValue: 999999.99 },
        module
      );

      // 验证折扣
      if (item.discount !== undefined && item.discount !== null) {
        if (item.discount < 0 || item.discount > 1) {
          throw new ErrorUtils.ValidationError(
            `订单项 ${index + 1} 的折扣必须在 0-1 之间`,
            { index, discount: item.discount },
            module
          );
        }
      }

      // 验证计算结果
      const itemTotal = item.price * item.quantity * (1 - (item.discount || 0));
      if (itemTotal < 0) {
        throw new ErrorUtils.BusinessLogicError(
          `订单项 ${index + 1} 的计算结果不能为负数`,
          { index, itemTotal, price: item.price, quantity: item.quantity, discount: item.discount },
          module
        );
      }
    }
  }

  /**
   * 计算并验证订单总金额
   * 
   * @param orderItems 订单项列表
   * @param module 模块名称
   * @returns 计算后的总金额
   */
  static async calculateAndValidateOrderTotal(
    orderItems: Array<{
      productId: number;
      quantity: number;
      price: number;
      discount?: number;
    }>,
    module: string = 'order-calculation'
  ): Promise<number> {
    // 先验证所有订单项
    await this.validateOrderItemPrices(orderItems, module);

    // 计算总金额
    const totalAmount = orderItems.reduce((sum, item) => {
      const itemTotal = item.price * item.quantity * (1 - (item.discount || 0));
      return sum + itemTotal;
    }, 0);

    // 验证总金额
    if (totalAmount < 0) {
      throw new ErrorUtils.BusinessLogicError(
        "订单总金额不能为负数",
        { totalAmount, orderItems },
        module
      );
    }

    if (totalAmount > 9999999.99) {
      throw new ErrorUtils.BusinessLogicError(
        "订单总金额超过系统限制",
        { totalAmount, maxAmount: 9999999.99 },
        module
      );
    }

    return Math.round(totalAmount * 100) / 100; // 保留两位小数
  }
}
