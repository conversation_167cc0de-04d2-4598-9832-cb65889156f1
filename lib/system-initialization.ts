/**
 * 系统初始化管理器 - 解决重复初始化问题
 * 
 * 针对日志中发现的重复系统初始化问题的解决方案
 */

interface InitStatus {
  initialized: boolean;
  initPromise: Promise<void> | null;
  timestamp: number;
}

class SystemInitializationManager {
  private static instance: SystemInitializationManager;
  private initStatus: InitStatus = {
    initialized: false,
    initPromise: null,
    timestamp: 0
  };

  private constructor() {}

  static getInstance(): SystemInitializationManager {
    if (!SystemInitializationManager.instance) {
      SystemInitializationManager.instance = new SystemInitializationManager();
    }
    return SystemInitializationManager.instance;
  }

  /**
   * 🚀 智能系统初始化 - 避免重复初始化
   */
  async initialize(): Promise<void> {
    // 检查是否已经初始化
    if (this.initStatus.initialized) {
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ [系统] 已初始化，跳过重复初始化');
      }
      return;
    }

    // 检查是否正在初始化中
    if (this.initStatus.initPromise) {
      if (process.env.NODE_ENV === 'development') {
        console.log('⏳ [系统] 正在初始化中，等待完成...');
      }
      return this.initStatus.initPromise;
    }

    // 开始初始化
    console.log('🚀 [系统] 开始初始化...');
    this.initStatus.initPromise = this.performInitialization();
    
    try {
      await this.initStatus.initPromise;
      this.initStatus.initialized = true;
      this.initStatus.timestamp = Date.now();
      console.log('✅ [系统] 初始化完成');
    } catch (error) {
      console.error('❌ [系统] 初始化失败:', error);
      this.initStatus.initPromise = null;
      throw error;
    }
  }

  /**
   * 执行实际的初始化操作
   */
  private async performInitialization(): Promise<void> {
    const startTime = performance.now();

    try {
      // 并行初始化各个模块，提升性能
      await Promise.all([
        this.initializeDatabase(),
        this.initializeAuth(),
        this.initializeCache(),
        this.initializeLogging()
      ]);

      const endTime = performance.now();
      console.log(`⚡ [系统] 初始化耗时: ${(endTime - startTime).toFixed(2)}ms`);
    } catch (error) {
      console.error('💥 [系统] 模块初始化失败:', error);
      throw error;
    }
  }

  /**
   * 数据库初始化
   */
  private async initializeDatabase(): Promise<void> {
    try {
      // 检查数据库连接
      if (process.env.NODE_ENV === 'development') {
        console.log('🗄️ [系统] 数据库连接检查...');
      }
      
      // 这里可以添加数据库连接池初始化逻辑
      // await prisma.$connect();
      
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ [系统] 数据库初始化完成');
      }
    } catch (error) {
      console.error('❌ [系统] 数据库初始化失败:', error);
      throw error;
    }
  }

  /**
   * 认证系统初始化
   */
  private async initializeAuth(): Promise<void> {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('🔐 [系统] 认证系统初始化...');
      }
      
      // 预热NextAuth配置
      if (!process.env.NEXTAUTH_SECRET) {
        console.warn('⚠️ [系统] NEXTAUTH_SECRET 未设置');
      }
      
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ [系统] 认证系统初始化完成');
      }
    } catch (error) {
      console.error('❌ [系统] 认证系统初始化失败:', error);
      throw error;
    }
  }

  /**
   * 缓存系统初始化
   */
  private async initializeCache(): Promise<void> {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('💾 [系统] 缓存系统初始化...');
      }
      
      // 初始化内存缓存
      global.__systemCache = global.__systemCache || new Map();
      
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ [系统] 缓存系统初始化完成');
      }
    } catch (error) {
      console.error('❌ [系统] 缓存系统初始化失败:', error);
      throw error;
    }
  }

  /**
   * 日志系统初始化
   */
  private async initializeLogging(): Promise<void> {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('📝 [系统] 日志系统初始化...');
      }
      
      // 设置日志级别
      const logLevel = process.env.LOG_LEVEL || 'info';
      console.log(`📊 [系统] 日志级别设置为: ${logLevel}`);
      
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ [系统] 日志系统初始化完成');
      }
    } catch (error) {
      console.error('❌ [系统] 日志系统初始化失败:', error);
      throw error;
    }
  }

  /**
   * 获取初始化状态
   */
  getStatus(): InitStatus {
    return { ...this.initStatus };
  }

  /**
   * 重置初始化状态（用于测试或重启）
   */
  reset(): void {
    console.log('🔄 [系统] 重置初始化状态');
    this.initStatus = {
      initialized: false,
      initPromise: null,
      timestamp: 0
    };
  }

  /**
   * 检查系统健康状态
   */
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.initStatus.initialized) {
        return false;
      }

      // 检查初始化时间是否过久（超过1小时重新初始化）
      const oneHour = 60 * 60 * 1000;
      if (Date.now() - this.initStatus.timestamp > oneHour) {
        console.log('⏰ [系统] 初始化时间过久，建议重新初始化');
        return false;
      }

      return true;
    } catch (error) {
      console.error('❌ [系统] 健康检查失败:', error);
      return false;
    }
  }
}

// 导出单例实例
export const systemManager = SystemInitializationManager.getInstance();

/**
 * 快捷初始化函数 - 供其他模块使用
 */
export async function ensureSystemInitialized(): Promise<void> {
  await systemManager.initialize();
}

/**
 * 中间件：确保系统已初始化
 */
export function withSystemInitialization<T extends (...args: any[]) => any>(
  handler: T
): T {
  return (async (...args: any[]) => {
    await ensureSystemInitialized();
    return handler(...args);
  }) as T;
}

// 开发环境启动提示
if (process.env.NODE_ENV === 'development') {
  console.log(`
🚀 系统初始化管理器已加载
📊 功能特性：
   ✅ 单例模式避免重复初始化
   ✅ 并行初始化提升性能
   ✅ 健康检查和状态监控
   ✅ 智能缓存和错误处理

💡 使用方法：
   import { ensureSystemInitialized } from '@/lib/system-initialization'
   await ensureSystemInitialized()
  `);
}

export default SystemInitializationManager;