/**
 * 事务管理器
 * 
 * 提供统一的数据库事务管理，支持重试机制和错误处理
 * 
 * @module TransactionManager
 * @category 核心模块
 */

import prisma from "@/lib/db";
import { ErrorUtils } from "@/lib/error-utils";

/**
 * 事务选项
 */
export interface TransactionOptions {
  maxRetries?: number;
  retryDelay?: number;
  timeout?: number;
  isolationLevel?: 'ReadCommitted' | 'Serializable' | 'RepeatableRead' | 'ReadUncommitted';
}

/**
 * 事务上下文
 */
export type TransactionContext = Parameters<Parameters<typeof prisma.$transaction>[0]>[0];

/**
 * 事务管理器类
 */
export class TransactionManager {
  private static readonly DEFAULT_OPTIONS: Required<TransactionOptions> = {
    maxRetries: 3,
    retryDelay: 100,
    timeout: 10000,
    isolationLevel: 'ReadCommitted'
  };

  /**
   * 执行事务，支持重试机制
   * 
   * @param operation 事务操作函数
   * @param options 事务选项
   * @returns 事务执行结果
   */
  static async executeTransaction<T>(
    operation: (tx: TransactionContext) => Promise<T>,
    options: TransactionOptions = {}
  ): Promise<T> {
    const finalOptions = { ...this.DEFAULT_OPTIONS, ...options };
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= finalOptions.maxRetries; attempt++) {
      try {
        const result = await prisma.$transaction(
          operation,
          {
            maxWait: finalOptions.timeout,
            timeout: finalOptions.timeout,
            isolationLevel: finalOptions.isolationLevel === 'ReadCommitted' ? undefined : 
                           finalOptions.isolationLevel as any
          }
        );
        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        // 检查是否为可重试的错误
        if (this.isRetriableError(lastError) && attempt < finalOptions.maxRetries) {
          // 等待一段时间后重试，使用指数退避算法
          const delay = finalOptions.retryDelay * Math.pow(2, attempt - 1);
          await this.sleep(delay);
          continue;
        }
        
        // 不可重试的错误或达到最大重试次数
        break;
      }
    }

    // 抛出最后一次的错误
    throw new ErrorUtils.TransactionError(
      `事务执行失败，已重试 ${finalOptions.maxRetries} 次`,
      { 
        originalError: lastError?.message,
        attempts: finalOptions.maxRetries 
      },
      "transaction-manager"
    );
  }

  /**
   * 执行带悲观锁的查询
   * 注意：Prisma 不直接支持 FOR UPDATE，我们通过在事务中查询并立即更新来模拟
   * 
   * @param tx 事务上下文
   * @param model 模型名称
   * @param where 查询条件
   * @returns 查询结果
   */
  static async findWithLock<T>(
    tx: TransactionContext,
    model: keyof TransactionContext,
    where: any
  ): Promise<T | null> {
    // 在 Prisma 中模拟悲观锁：先查询，然后立即尝试更新版本号
    const record = await (tx[model] as any).findFirst({ where });
    
    if (record && 'version' in record) {
      // 如果记录有版本字段，尝试更新版本号来获得锁
      const updated = await (tx[model] as any).updateMany({
        where: { ...where, version: record.version },
        data: { version: record.version + 1 }
      });
      
      if (updated.count === 0) {
        throw new ErrorUtils.ConcurrencyError(
          "记录已被其他操作修改，请重试",
          { where },
          "transaction-manager"
        );
      }
      
      return { ...record, version: record.version + 1 };
    }
    
    return record;
  }

  /**
   * 执行库存原子操作
   * 
   * @param operation 库存操作函数
   * @param options 事务选项
   * @returns 操作结果
   */
  static async executeInventoryOperation<T>(
    operation: (tx: TransactionContext) => Promise<T>,
    options: TransactionOptions = {}
  ): Promise<T> {
    return this.executeTransaction(operation, {
      ...options,
      isolationLevel: 'Serializable' // 库存操作使用更严格的隔离级别
    });
  }

  /**
   * 检查错误是否可重试
   * 
   * @param error 错误对象
   * @returns 是否可重试
   */
  private static isRetriableError(error: Error): boolean {
    const retriableMessages = [
      'connection',
      'timeout',
      'lock',
      'deadlock',
      'serialization',
      'concurrent',
      'retry'
    ];
    
    const message = error.message.toLowerCase();
    return retriableMessages.some(keyword => message.includes(keyword));
  }

  /**
   * 休眠指定时间
   * 
   * @param ms 毫秒数
   */
  private static sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 创建库存变动记录
   * 
   * @param tx 事务上下文
   * @param data 变动记录数据
   */
  static async createInventoryTransaction(
    tx: TransactionContext,
    data: {
      productId: number;
      quantity: number;
      type: string;
      notes?: string;
      sourceWarehouseId?: number | null;
      targetWarehouseId?: number | null;
      referenceId?: string | null;
      referenceType?: string | null;
    }
  ) {
    return await tx.inventoryTransaction.create({
      data: {
        productId: data.productId,
        quantity: data.quantity,
        type: data.type,
        notes: data.notes || '',
        sourceWarehouseId: data.sourceWarehouseId,
        targetWarehouseId: data.targetWarehouseId,
        referenceId: data.referenceId,
        referenceType: data.referenceType,
        createdAt: new Date()
      }
    });
  }

  /**
   * 原子性更新库存数量（使用乐观锁）
   *
   * @param tx 事务上下文
   * @param inventoryId 库存ID
   * @param quantityChange 数量变化（正数为增加，负数为减少）
   * @param checkMinimum 是否检查最小库存
   * @returns 更新后的库存记录
   */
  static async updateInventoryQuantity(
    tx: TransactionContext,
    inventoryId: number,
    quantityChange: number,
    checkMinimum: boolean = true
  ) {
    // 先获取当前库存记录（包含版本号）
    const currentInventory = await tx.inventoryItem.findUnique({
      where: { id: inventoryId }
    });

    if (!currentInventory) {
      throw new ErrorUtils.NotFoundError(
        "库存记录不存在",
        { inventoryId },
        "transaction-manager"
      );
    }

    const newQuantity = currentInventory.quantity + quantityChange;

    // 检查库存是否足够
    if (newQuantity < 0) {
      throw new ErrorUtils.BusinessLogicError(
        "库存不足",
        {
          inventoryId,
          currentQuantity: currentInventory.quantity,
          requestedChange: quantityChange,
          resultingQuantity: newQuantity
        },
        "transaction-manager"
      );
    }

    // 检查最小库存
    if (checkMinimum && currentInventory.minQuantity && newQuantity < currentInventory.minQuantity) {
      throw new ErrorUtils.BusinessLogicError(
        "操作后库存将低于最小库存要求",
        {
          inventoryId,
          newQuantity,
          minQuantity: currentInventory.minQuantity
        },
        "transaction-manager"
      );
    }

    // 使用乐观锁更新库存数量
    const updateResult = await tx.inventoryItem.updateMany({
      where: {
        id: inventoryId,
        version: currentInventory.version // 乐观锁：只有版本号匹配才能更新
      },
      data: {
        quantity: newQuantity,
        version: currentInventory.version + 1 // 更新版本号
      }
    });

    // 检查是否更新成功
    if (updateResult.count === 0) {
      throw new ErrorUtils.ConcurrencyError(
        "库存记录已被其他操作修改，请重试",
        {
          inventoryId,
          expectedVersion: currentInventory.version,
          quantityChange
        },
        "transaction-manager"
      );
    }

    // 返回更新后的记录
    const updatedInventory = await tx.inventoryItem.findUnique({
      where: { id: inventoryId }
    });

    return updatedInventory!;
  }

  /**
   * 获取或创建库存记录
   * 
   * @param tx 事务上下文
   * @param productId 产品ID
   * @param warehouseId 仓库ID
   * @param initialQuantity 初始数量（如果需要创建）
   * @returns 库存记录
   */
  static async getOrCreateInventory(
    tx: TransactionContext,
    productId: number,
    warehouseId: number,
    initialQuantity: number = 0
  ) {
    // 尝试查找现有库存记录
    let inventory = await tx.inventoryItem.findFirst({
      where: {
        productId,
        warehouseId
      }
    });

    if (!inventory) {
      // 创建新的库存记录（版本号默认为1）
      inventory = await tx.inventoryItem.create({
        data: {
          productId,
          warehouseId,
          quantity: initialQuantity,
          version: 1
        }
      });
    }

    return inventory;
  }
}