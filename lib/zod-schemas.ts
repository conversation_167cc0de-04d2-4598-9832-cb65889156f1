/**
 * Zod验证Schema定义
 * 
 * 为API输入提供类型安全的验证schema
 * 
 * @module ZodSchemas
 * @category 验证模块
 */

import { z } from "zod";

/**
 * 基础验证Schema
 */
export const BaseSchemas = {
  // ID验证
  id: z.number().int().positive("ID必须是正整数"),
  
  // 可选ID验证
  optionalId: z.number().int().positive("ID必须是正整数").optional(),
  
  // 字符串验证
  nonEmptyString: z.string().min(1, "不能为空字符串").trim(),
  
  // 可选字符串验证
  optionalString: z.string().trim().optional(),
  
  // 价格验证
  price: z.number()
    .min(0, "价格不能为负数")
    .max(999999.99, "价格不能超过999999.99")
    .refine(
      (val) => {
        const decimalPlaces = (val.toString().split('.')[1] || '').length;
        return decimalPlaces <= 2;
      },
      "价格最多只能有2位小数"
    ),
  
  // 可选价格验证
  optionalPrice: z.number()
    .min(0, "价格不能为负数")
    .max(999999.99, "价格不能超过999999.99")
    .refine(
      (val) => {
        const decimalPlaces = (val.toString().split('.')[1] || '').length;
        return decimalPlaces <= 2;
      },
      "价格最多只能有2位小数"
    )
    .optional(),
  
  // 百分比验证（0-100）
  percentage: z.number()
    .min(0, "百分比不能为负数")
    .max(100, "百分比不能超过100"),
  
  // 数量验证
  quantity: z.number().int().min(1, "数量必须是正整数"),
  
  // 折扣验证（0-1）
  discount: z.number()
    .min(0, "折扣不能为负数")
    .max(1, "折扣不能超过1")
    .optional(),
  
  // 日期验证
  dateString: z.string().refine(
    (val) => !isNaN(Date.parse(val)),
    "无效的日期格式"
  ),
  
  // 可选日期验证
  optionalDateString: z.string()
    .refine((val) => !isNaN(Date.parse(val)), "无效的日期格式")
    .optional(),
};

/**
 * 产品相关Schema
 */
export const ProductSchemas = {
  // 产品创建Schema
  create: z.object({
    name: BaseSchemas.nonEmptyString.max(255, "产品名称不能超过255个字符"),
    description: BaseSchemas.optionalString,
    categoryId: z.union([
      BaseSchemas.optionalId,
      z.literal("uncategorized"),
      z.null()
    ]).optional(),
    price: BaseSchemas.optionalPrice,
    commissionRate: BaseSchemas.percentage.optional(),
    material: BaseSchemas.optionalString,
    unit: BaseSchemas.optionalString,
    tags: z.array(z.string()).optional(),
    images: z.array(z.string().url("无效的图片URL")).optional(),
  }),
  
  // 产品更新Schema
  update: z.object({
    name: BaseSchemas.nonEmptyString.max(255, "产品名称不能超过255个字符").optional(),
    description: BaseSchemas.optionalString,
    categoryId: z.union([
      BaseSchemas.optionalId,
      z.literal("uncategorized"),
      z.null()
    ]).optional(),
    price: BaseSchemas.optionalPrice,
    commissionRate: BaseSchemas.percentage.optional(),
    material: BaseSchemas.optionalString,
    unit: BaseSchemas.optionalString,
    tags: z.array(z.string()).optional(),
    images: z.array(z.string().url("无效的图片URL")).optional(),
  }),
  
  // 产品查询Schema
  query: z.object({
    page: z.number().int().min(1, "页码必须大于0").optional(),
    limit: z.number().int().min(1, "每页数量必须大于0").max(100, "每页数量不能超过100").optional(),
    search: BaseSchemas.optionalString,
    categoryId: BaseSchemas.optionalId,
    material: BaseSchemas.optionalString,
    minPrice: BaseSchemas.optionalPrice,
    maxPrice: BaseSchemas.optionalPrice,
  }).refine(
    (data) => {
      if (data.minPrice && data.maxPrice) {
        return data.minPrice <= data.maxPrice;
      }
      return true;
    },
    "最小价格不能大于最大价格"
  ),
};

/**
 * 订单相关Schema
 */
export const OrderSchemas = {
  // 订单项Schema
  orderItem: z.object({
    productId: BaseSchemas.id,
    quantity: BaseSchemas.quantity,
    price: BaseSchemas.price,
    discount: BaseSchemas.discount,
    notes: BaseSchemas.optionalString,
  }),
  
  // 订单创建Schema
  create: z.object({
    customerId: BaseSchemas.id,
    employeeId: BaseSchemas.id,
    warehouseId: BaseSchemas.optionalId,
    orderDate: BaseSchemas.optionalDateString,
    status: z.enum(["pending", "confirmed", "completed", "cancelled"]).optional(),
    totalAmount: BaseSchemas.price,
    paidAmount: BaseSchemas.optionalPrice,
    paymentStatus: z.enum(["unpaid", "partial", "paid"]).optional(),
    paymentMethod: BaseSchemas.optionalString,
    notes: BaseSchemas.optionalString,
    items: z.array(z.object({
      productId: BaseSchemas.id,
      quantity: BaseSchemas.quantity,
      price: BaseSchemas.price,
      discount: BaseSchemas.discount,
      notes: BaseSchemas.optionalString,
    })).min(1, "订单必须包含至少一个商品"),
  }),
  
  // 订单更新Schema
  update: z.object({
    customerId: BaseSchemas.optionalId,
    employeeId: BaseSchemas.optionalId,
    orderDate: BaseSchemas.optionalDateString,
    status: z.enum(["pending", "confirmed", "completed", "cancelled"]).optional(),
    totalAmount: BaseSchemas.optionalPrice,
    paidAmount: BaseSchemas.optionalPrice,
    paymentStatus: z.enum(["unpaid", "partial", "paid"]).optional(),
    paymentMethod: BaseSchemas.optionalString,
    notes: BaseSchemas.optionalString,
  }),
  
  // 订单查询Schema
  query: z.object({
    page: z.number().int().min(1, "页码必须大于0").optional(),
    limit: z.number().int().min(1, "每页数量必须大于0").max(100, "每页数量不能超过100").optional(),
    customerId: BaseSchemas.optionalId,
    employeeId: BaseSchemas.optionalId,
    status: z.enum(["pending", "confirmed", "completed", "cancelled"]).optional(),
    paymentStatus: z.enum(["unpaid", "partial", "paid"]).optional(),
    startDate: BaseSchemas.optionalDateString,
    endDate: BaseSchemas.optionalDateString,
  }).refine(
    (data) => {
      if (data.startDate && data.endDate) {
        return new Date(data.startDate) <= new Date(data.endDate);
      }
      return true;
    },
    "开始日期不能晚于结束日期"
  ),
};

/**
 * 客户相关Schema
 */
export const CustomerSchemas = {
  // 客户创建Schema
  create: z.object({
    name: BaseSchemas.nonEmptyString.max(100, "客户名称不能超过100个字符"),
    phone: z.string()
      .regex(/^1[3-9]\d{9}$/, "无效的手机号码格式")
      .optional(),
    email: z.string().email("无效的邮箱格式").optional(),
    address: BaseSchemas.optionalString,
    notes: BaseSchemas.optionalString,
  }),
  
  // 客户更新Schema
  update: z.object({
    name: BaseSchemas.nonEmptyString.max(100, "客户名称不能超过100个字符").optional(),
    phone: z.string()
      .regex(/^1[3-9]\d{9}$/, "无效的手机号码格式")
      .optional(),
    email: z.string().email("无效的邮箱格式").optional(),
    address: BaseSchemas.optionalString,
    notes: BaseSchemas.optionalString,
  }),
};

/**
 * 员工相关Schema
 */
export const EmployeeSchemas = {
  // 员工创建Schema
  create: z.object({
    name: BaseSchemas.nonEmptyString.max(50, "员工姓名不能超过50个字符"),
    position: BaseSchemas.optionalString,
    phone: z.string()
      .regex(/^1[3-9]\d{9}$/, "无效的手机号码格式")
      .optional(),
    email: z.string().email("无效的邮箱格式").optional(),
    hireDate: BaseSchemas.optionalDateString,
    salary: BaseSchemas.optionalPrice,
    commissionRate: BaseSchemas.percentage.optional(),
    isActive: z.boolean().optional(),
  }),
  
  // 员工更新Schema
  update: z.object({
    name: BaseSchemas.nonEmptyString.max(50, "员工姓名不能超过50个字符").optional(),
    position: BaseSchemas.optionalString,
    phone: z.string()
      .regex(/^1[3-9]\d{9}$/, "无效的手机号码格式")
      .optional(),
    email: z.string().email("无效的邮箱格式").optional(),
    hireDate: BaseSchemas.optionalDateString,
    salary: BaseSchemas.optionalPrice,
    commissionRate: BaseSchemas.percentage.optional(),
    isActive: z.boolean().optional(),
  }),
};

/**
 * 库存相关Schema
 */
export const InventorySchemas = {
  // 库存查询Schema
  query: z.object({
    page: z.number().int().min(1, "页码必须大于0").optional(),
    limit: z.number().int().min(1, "每页数量必须大于0").max(100, "每页数量不能超过100").optional(),
    warehouseId: BaseSchemas.optionalId,
    productId: BaseSchemas.optionalId,
    lowStock: z.boolean().optional(),
  }),
  
  // 库存更新Schema
  update: z.object({
    quantity: z.number().int().min(0, "库存数量不能为负数"),
    minQuantity: z.number().int().min(0, "最小库存不能为负数").optional(),
    notes: BaseSchemas.optionalString,
  }),
};
