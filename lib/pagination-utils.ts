/**
 * 分页工具模块
 * 
 * 提供统一的分页参数处理、查询构建和响应格式化功能
 * 
 * @module 分页工具
 * @category 核心模块
 */

/**
 * 分页参数接口
 */
export interface PaginationParams {
  page?: number;
  pageSize?: number;
  offset?: number;
  limit?: number;
}

/**
 * 标准化的分页参数
 */
export interface NormalizedPaginationParams {
  offset: number;
  limit: number;
  page: number;
  pageSize: number;
}

/**
 * 分页响应接口
 */
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    total: number;
    offset: number;
    limit: number;
    page: number;
    pageSize: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * 分页配置
 */
export interface PaginationConfig {
  defaultLimit: number;
  maxLimit: number;
  defaultPage: number;
}

/**
 * 默认分页配置
 */
export const DEFAULT_PAGINATION_CONFIG: PaginationConfig = {
  defaultLimit: 20,
  maxLimit: 100,
  defaultPage: 1,
};

/**
 * 解析和标准化分页参数
 * 
 * 支持两种分页模式：
 * 1. page/pageSize 模式（前端友好）
 * 2. offset/limit 模式（数据库友好）
 * 
 * @param params 原始分页参数
 * @param config 分页配置
 * @returns 标准化的分页参数
 * 
 * @example
 * ```typescript
 * // 使用 page/pageSize
 * const params1 = normalizePaginationParams({ page: 2, pageSize: 10 });
 * // 结果: { offset: 10, limit: 10, page: 2, pageSize: 10 }
 * 
 * // 使用 offset/limit
 * const params2 = normalizePaginationParams({ offset: 20, limit: 15 });
 * // 结果: { offset: 20, limit: 15, page: 2, pageSize: 15 }
 * ```
 */
export function normalizePaginationParams(
  params: PaginationParams = {},
  config: PaginationConfig = DEFAULT_PAGINATION_CONFIG
): NormalizedPaginationParams {
  const { page, pageSize, offset, limit } = params;
  
  // 优先使用 offset/limit，如果没有则使用 page/pageSize
  let finalOffset: number;
  let finalLimit: number;
  
  if (offset !== undefined && limit !== undefined) {
    // offset/limit 模式
    finalOffset = Math.max(0, offset);
    finalLimit = Math.min(Math.max(1, limit), config.maxLimit);
  } else {
    // page/pageSize 模式
    const finalPageSize = pageSize 
      ? Math.min(Math.max(1, pageSize), config.maxLimit)
      : config.defaultLimit;
    const finalPage = Math.max(config.defaultPage, page || config.defaultPage);
    
    finalOffset = (finalPage - 1) * finalPageSize;
    finalLimit = finalPageSize;
  }
  
  // 计算对应的 page 和 pageSize
  const calculatedPageSize = finalLimit;
  const calculatedPage = Math.floor(finalOffset / calculatedPageSize) + 1;
  
  return {
    offset: finalOffset,
    limit: finalLimit,
    page: calculatedPage,
    pageSize: calculatedPageSize,
  };
}

/**
 * 从 URL 搜索参数中解析分页参数
 * 
 * @param searchParams URL 搜索参数对象
 * @param config 分页配置
 * @returns 标准化的分页参数
 * 
 * @example
 * ```typescript
 * const url = new URL('https://api.example.com/products?page=2&pageSize=15');
 * const params = parsePaginationFromSearchParams(url.searchParams);
 * ```
 */
export function parsePaginationFromSearchParams(
  searchParams: URLSearchParams,
  config: PaginationConfig = DEFAULT_PAGINATION_CONFIG
): NormalizedPaginationParams {
  const rawParams: PaginationParams = {
    page: searchParams.get('page') ? parseInt(searchParams.get('page')!) : undefined,
    pageSize: searchParams.get('pageSize') ? parseInt(searchParams.get('pageSize')!) : undefined,
    offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : undefined,
    limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined,
  };
  
  return normalizePaginationParams(rawParams, config);
}

/**
 * 构建分页响应对象
 * 
 * @param data 数据数组
 * @param total 总记录数
 * @param params 分页参数
 * @returns 分页响应对象
 * 
 * @example
 * ```typescript
 * const response = buildPaginatedResponse(
 *   products,
 *   150,
 *   { offset: 20, limit: 10, page: 3, pageSize: 10 }
 * );
 * ```
 */
export function buildPaginatedResponse<T>(
  data: T[],
  total: number,
  params: NormalizedPaginationParams
): PaginatedResponse<T> {
  const { offset, limit, page, pageSize } = params;
  const totalPages = Math.ceil(total / pageSize);
  
  return {
    data,
    pagination: {
      total,
      offset,
      limit,
      page,
      pageSize,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  };
}

/**
 * 验证分页参数
 * 
 * @param params 分页参数
 * @param config 分页配置
 * @returns 验证结果
 */
export function validatePaginationParams(
  params: PaginationParams,
  config: PaginationConfig = DEFAULT_PAGINATION_CONFIG
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // 验证 page
  if (params.page !== undefined) {
    if (!Number.isInteger(params.page) || params.page < 1) {
      errors.push('page 必须是大于等于 1 的整数');
    }
  }
  
  // 验证 pageSize
  if (params.pageSize !== undefined) {
    if (!Number.isInteger(params.pageSize) || params.pageSize < 1) {
      errors.push('pageSize 必须是大于等于 1 的整数');
    } else if (params.pageSize > config.maxLimit) {
      errors.push(`pageSize 不能超过 ${config.maxLimit}`);
    }
  }
  
  // 验证 offset
  if (params.offset !== undefined) {
    if (!Number.isInteger(params.offset) || params.offset < 0) {
      errors.push('offset 必须是大于等于 0 的整数');
    }
  }
  
  // 验证 limit
  if (params.limit !== undefined) {
    if (!Number.isInteger(params.limit) || params.limit < 1) {
      errors.push('limit 必须是大于等于 1 的整数');
    } else if (params.limit > config.maxLimit) {
      errors.push(`limit 不能超过 ${config.maxLimit}`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Prisma 分页查询帮助函数
 * 
 * @param params 分页参数
 * @returns Prisma 查询选项
 * 
 * @example
 * ```typescript
 * const paginationOptions = getPrismaQueryOptions(params);
 * const products = await prisma.product.findMany({
 *   ...paginationOptions,
 *   where: { status: 'active' },
 *   include: { category: true }
 * });
 * ```
 */
export function getPrismaQueryOptions(params: NormalizedPaginationParams) {
  return {
    skip: params.offset,
    take: params.limit,
  };
}

/**
 * 构建分页信息字符串（用于日志）
 * 
 * @param params 分页参数
 * @returns 分页信息字符串
 */
export function getPaginationLogInfo(params: NormalizedPaginationParams): string {
  return `page ${params.page} (offset ${params.offset}, limit ${params.limit})`;
}

/**
 * 计算总页数
 * 
 * @param total 总记录数
 * @param pageSize 每页大小
 * @returns 总页数
 */
export function calculateTotalPages(total: number, pageSize: number): number {
  return Math.ceil(Math.max(0, total) / Math.max(1, pageSize));
}

/**
 * 获取安全的分页范围（防止超出边界）
 * 
 * @param total 总记录数
 * @param params 分页参数
 * @returns 调整后的分页参数
 */
export function getSafePaginationRange(
  total: number,
  params: NormalizedPaginationParams
): NormalizedPaginationParams {
  const totalPages = calculateTotalPages(total, params.pageSize);
  
  if (totalPages === 0) {
    // 没有数据时返回第一页
    return {
      offset: 0,
      limit: params.limit,
      page: 1,
      pageSize: params.pageSize,
    };
  }
  
  if (params.page > totalPages) {
    // 超出范围时返回最后一页
    return {
      offset: (totalPages - 1) * params.pageSize,
      limit: params.limit,
      page: totalPages,
      pageSize: params.pageSize,
    };
  }
  
  return params;
}