import { PrismaClient } from "@prisma/client"

// PrismaClient is attached to the `global` object in development to prevent
// exhausting your database connection limit.
const globalForPrisma = global as unknown as { prisma: PrismaClient }

const prisma = globalForPrisma.prisma || new PrismaClient({
  log: [
    {
      emit: 'event',
      level: 'query',
    },
    {
      emit: 'event',
      level: 'error',
    },
    {
      emit: 'event',
      level: 'warn',
    },
  ],
})

// 添加查询性能监控
prisma.$on('query', (e) => {
  // 监控慢查询（超过1秒）
  if (e.duration > 1000) {
    console.warn(`[DB] 慢查询检测: ${e.query.substring(0, 100)}... 耗时 ${e.duration}ms`);
  }

  // 在开发环境中记录所有查询（可选）
  if (process.env.NODE_ENV === 'development' && process.env.DEBUG_DB === 'true') {
    console.log(`[DB] Query: ${e.query} (${e.duration}ms)`);
  }
});

// 添加数据库错误监控
prisma.$on('error', (e) => {
  console.error('[DB] 数据库错误:', e);
});

// 添加连接监控
if (process.env.NODE_ENV === 'production') {
  // 生产环境中定期检查连接状态
  setInterval(async () => {
    try {
      await prisma.$queryRaw`SELECT 1`;
    } catch (error) {
      console.error('[DB] 连接检查失败:', error);
    }
  }, 60000); // 每分钟检查一次
}

if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma

export default prisma
