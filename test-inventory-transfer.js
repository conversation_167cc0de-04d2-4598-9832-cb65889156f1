/**
 * 库存转移原子性测试脚本
 * 
 * 此脚本用于测试修复后的库存转移功能，验证原子性和并发控制是否正常工作
 */

import { transferInventory } from './lib/actions/inventory-actions.js';

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 测试基本库存转移功能
 */
async function testBasicTransfer() {
  console.log('\n=== 测试基本库存转移功能 ===');
  
  try {
    const result = await transferInventory({
      productId: 1,
      quantity: 5,
      fromLocationId: 1,
      toLocationId: 2,
      notes: '基本转移测试'
    });
    
    console.log('✅ 基本转移测试成功:', result);
  } catch (error) {
    console.error('❌ 基本转移测试失败:', error.message);
  }
}

/**
 * 测试并发库存转移
 */
async function testConcurrentTransfer() {
  console.log('\n=== 测试并发库存转移 ===');
  
  const concurrentTransfers = [];
  
  // 创建5个并发转移操作
  for (let i = 0; i < 5; i++) {
    concurrentTransfers.push(
      transferInventory({
        productId: 1,
        quantity: 2,
        fromLocationId: 1,
        toLocationId: 2,
        notes: `并发转移测试 #${i + 1}`
      }).catch(error => ({
        error: true,
        message: error.message,
        index: i + 1
      }))
    );
  }
  
  try {
    const results = await Promise.all(concurrentTransfers);
    
    const successes = results.filter(r => !r.error);
    const failures = results.filter(r => r.error);
    
    console.log(`✅ 成功的转移操作: ${successes.length}`);
    console.log(`❌ 失败的转移操作: ${failures.length}`);
    
    failures.forEach(failure => {
      console.log(`   - 转移 #${failure.index}: ${failure.message}`);
    });
    
  } catch (error) {
    console.error('❌ 并发转移测试失败:', error.message);
  }
}

/**
 * 测试库存不足的情况
 */
async function testInsufficientStock() {
  console.log('\n=== 测试库存不足情况 ===');
  
  try {
    const result = await transferInventory({
      productId: 1,
      quantity: 999999, // 远超实际库存
      fromLocationId: 1,
      toLocationId: 2,
      notes: '库存不足测试'
    });
    
    console.log('❌ 库存不足测试应该失败，但成功了:', result);
  } catch (error) {
    if (error.message.includes('库存不足')) {
      console.log('✅ 库存不足测试成功，正确拒绝了转移:', error.message);
    } else {
      console.error('❌ 库存不足测试失败，错误类型不正确:', error.message);
    }
  }
}

/**
 * 测试无效参数
 */
async function testInvalidParameters() {
  console.log('\n=== 测试无效参数 ===');
  
  const testCases = [
    {
      name: '无效产品ID',
      data: { productId: 99999, quantity: 5, fromLocationId: 1, toLocationId: 2 }
    },
    {
      name: '无效源仓库ID',
      data: { productId: 1, quantity: 5, fromLocationId: 99999, toLocationId: 2 }
    },
    {
      name: '无效目标仓库ID',
      data: { productId: 1, quantity: 5, fromLocationId: 1, toLocationId: 99999 }
    },
    {
      name: '相同的源和目标仓库',
      data: { productId: 1, quantity: 5, fromLocationId: 1, toLocationId: 1 }
    },
    {
      name: '负数量',
      data: { productId: 1, quantity: -5, fromLocationId: 1, toLocationId: 2 }
    }
  ];
  
  for (const testCase of testCases) {
    try {
      const result = await transferInventory(testCase.data);
      console.log(`❌ ${testCase.name}测试应该失败，但成功了:`, result);
    } catch (error) {
      console.log(`✅ ${testCase.name}测试成功，正确拒绝了请求:`, error.message);
    }
  }
}

/**
 * 执行性能测试
 */
async function testPerformance() {
  console.log('\n=== 性能测试 ===');
  
  const startTime = Date.now();
  const transfers = [];
  
  // 执行50个转移操作
  for (let i = 0; i < 50; i++) {
    transfers.push(
      transferInventory({
        productId: 1,
        quantity: 1,
        fromLocationId: 1,
        toLocationId: 2,
        notes: `性能测试 #${i + 1}`
      }).catch(error => ({ error: true, message: error.message }))
    );
    
    // 间隔10ms避免过度并发
    if (i % 10 === 0) {
      await sleep(10);
    }
  }
  
  try {
    const results = await Promise.all(transfers);
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    const successes = results.filter(r => !r.error).length;
    const failures = results.filter(r => r.error).length;
    
    console.log(`⏱️  性能测试结果:`);
    console.log(`   - 总时间: ${duration}ms`);
    console.log(`   - 平均时间: ${(duration / 50).toFixed(2)}ms/操作`);
    console.log(`   - 成功操作: ${successes}`);
    console.log(`   - 失败操作: ${failures}`);
    console.log(`   - 成功率: ${((successes / 50) * 100).toFixed(1)}%`);
    
  } catch (error) {
    console.error('❌ 性能测试失败:', error.message);
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始库存转移原子性测试\n');
  
  await testBasicTransfer();
  await sleep(1000);
  
  await testConcurrentTransfer();
  await sleep(1000);
  
  await testInsufficientStock();
  await sleep(1000);
  
  await testInvalidParameters();
  await sleep(1000);
  
  await testPerformance();
  
  console.log('\n✅ 所有测试完成！');
  console.log('📝 注意：如果您看到任何❌标记的测试失败，请检查库存转移实现。');
}

// 执行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests().catch(error => {
    console.error('💥 测试执行失败:', error);
    process.exit(1);
  });
}

export {
  testBasicTransfer,
  testConcurrentTransfer,
  testInsufficientStock,
  testInvalidParameters,
  testPerformance
};