# 数据库修复指南 - Role 表缺失问题

## 问题诊断

**错误信息：** P2021 - The table `public.Role` does not exist in the current database
**发生位置：** `lib/init-account-system.ts` 第13行 `prisma.role.count()` 调用
**根本原因：** 数据库迁移没有正确应用，导致 Role 表及相关权限管理表缺失

## 修复方案（按推荐顺序）

### 方案 1：应用数据库迁移（推荐）

```bash
# 1. 检查迁移状态
npx prisma migrate status

# 2. 应用所有待处理的迁移
npx prisma migrate deploy

# 3. 重新生成 Prisma 客户端
npx prisma generate
```

### 方案 2：强制同步数据库结构（开发环境）

```bash
# 1. 强制推送 schema 到数据库（会覆盖现有结构）
npx prisma db push --force-reset

# 2. 重新生成 Prisma 客户端
npx prisma generate
```

### 方案 3：重置数据库（仅限开发环境，会丢失所有数据）

```bash
# 1. 重置数据库
npx prisma migrate reset

# 2. 重新生成 Prisma 客户端
npx prisma generate
```

## 预检查步骤

在执行修复前，建议先检查：

### 1. 检查 PostgreSQL 服务状态

```bash
# macOS
brew services list | grep postgresql

# 或者检查进程
ps aux | grep postgres
```

### 2. 检查数据库连接

```bash
# 使用 psql 连接数据库
psql postgresql://postgres:postgres@localhost:5432/linghua_enamel_gallery

# 在 psql 中列出所有表
\dt
```

### 3. 检查现有数据

```bash
# 运行项目中的数据库诊断脚本
node scripts/test-db-connection.js

# 或者运行完整诊断
node scripts/run-database-diagnostic.js
```

## 修复后验证

### 1. 验证表结构

```bash
# 检查 Role 表是否存在
npx prisma db execute --stdin
# 然后输入：SELECT COUNT(*) FROM "Role";
```

### 2. 验证应用启动

```bash
# 启动开发服务器
npm run dev

# 检查控制台是否还有 P2021 错误
```

### 3. 验证账户管理系统

访问 http://localhost:3000 并检查：
- 应用是否正常加载
- 控制台是否显示"账号管理系统初始化完成"
- 用户权限功能是否正常

## 数据备份（重要）

在执行任何修复操作前，建议备份现有数据：

```bash
# 创建数据库备份
pg_dump postgresql://postgres:postgres@localhost:5432/linghua_enamel_gallery > backup_$(date +%Y%m%d_%H%M%S).sql

# 或者使用项目中的备份脚本
node scripts/create-database-backup.js
```

## 常见问题排查

### 问题 1：PostgreSQL 服务未运行

```bash
# macOS 启动 PostgreSQL
brew services start postgresql

# 或者使用 pg_ctl
pg_ctl -D /usr/local/var/postgres start
```

### 问题 2：数据库不存在

```bash
# 创建数据库
createdb linghua_enamel_gallery

# 或者在 psql 中
CREATE DATABASE linghua_enamel_gallery;
```

### 问题 3：权限问题

```bash
# 检查用户权限
psql -c "SELECT current_user, session_user;"

# 如果需要，创建用户并授权
psql -c "CREATE USER postgres WITH PASSWORD 'postgres';"
psql -c "GRANT ALL PRIVILEGES ON DATABASE linghua_enamel_gallery TO postgres;"
```

## 预防措施

1. **定期备份：** 设置自动备份策略
2. **迁移管理：** 始终通过 Prisma 迁移管理数据库结构变更
3. **环境一致性：** 确保开发、测试、生产环境的迁移状态一致
4. **监控：** 实施数据库健康检查和监控

## 紧急联系

如果以上方法都无法解决问题，请：
1. 保存错误日志和数据库状态信息
2. 创建数据库备份
3. 联系技术支持团队
