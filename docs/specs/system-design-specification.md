# 聆花珐琅馆ERP系统设计规范

**文档版本**: v1.0  
**创建日期**: 2025-06-28  
**最后更新**: 2025-06-28  
**文档状态**: 正式版  
**维护人员**: 开发团队  

## 文档概述

本文档详细描述了聆花珐琅馆ERP系统的整体架构设计、技术选型、数据库设计、接口规范等核心技术内容，为系统开发、部署和维护提供技术指导。

## 目录

1. [系统概述](#1-系统概述)
2. [架构设计](#2-架构设计)
3. [技术栈规范](#3-技术栈规范)
4. [数据库设计](#4-数据库设计)
5. [API设计规范](#5-api设计规范)
6. [安全设计](#6-安全设计)
7. [部署架构](#7-部署架构)
8. [性能设计](#8-性能设计)
9. [监控和日志](#9-监控和日志)
10. [扩展性设计](#10-扩展性设计)

---

## 1. 系统概述

### 1.1 系统定位

聆花珐琅馆ERP系统是一个专为珐琅艺术品行业设计的企业资源规划系统，集成了产品管理、库存控制、订单处理、财务管理、人力资源、工坊管理等核心业务功能。

### 1.2 设计目标

- **业务完整性**: 覆盖珐琅艺术品从设计到销售的完整业务链
- **用户友好性**: 提供直观易用的操作界面
- **数据安全性**: 确保业务数据的安全性和完整性
- **系统稳定性**: 保证7×24小时稳定运行
- **扩展灵活性**: 支持业务发展和功能扩展需求### 1.3 核心特性

- **全栈TypeScript开发**: 类型安全，开发效率高
- **现代化UI设计**: 基于Radix UI + Tailwind CSS
- **完整权限管理**: 基于角色的访问控制(RBAC)
- **实时数据同步**: 支持多用户协作
- **移动端适配**: 响应式设计，支持移动设备
- **容器化部署**: Docker支持，部署便捷

---

## 2. 架构设计

### 2.1 整体架构

系统采用**单体应用架构**，基于Next.js全栈框架开发：

```
┌─────────────────────────────────────────────────────────┐
│                    客户端层                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │
│  │   桌面端    │  │   移动端    │  │   平板端    │      │
│  │  (React)    │  │  (React)    │  │  (React)    │      │
│  └─────────────┘  └─────────────┘  └─────────────┘      │
└─────────────────────────────────────────────────────────┘
                            │
                    HTTPS/WebSocket
                            │
┌─────────────────────────────────────────────────────────┐
│                   应用服务层                             │
│  ┌─────────────────────────────────────────────────────┐ │
│  │              Next.js App Router                     │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │ │
│  │  │  页面路由   │  │  API路由    │  │  中间件     │  │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  │ │
│  └─────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────┐ │
│  │                业务逻辑层                            │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │ │
│  │  │  认证授权   │  │  业务服务   │  │  数据验证   │  │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
                            │
                      Prisma ORM
                            │
┌─────────────────────────────────────────────────────────┐
│                   数据存储层                             │
│  ┌─────────────────────────────────────────────────────┐ │
│  │              PostgreSQL 15                          │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │ │
│  │  │  业务数据   │  │  系统数据   │  │  日志数据   │  │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```### 2.2 分层架构说明

**表现层 (Presentation Layer)**:
- 负责用户界面展示和交互
- 基于React组件化开发
- 支持桌面端和移动端

**业务逻辑层 (Business Logic Layer)**:
- 处理核心业务逻辑
- 数据验证和转换
- 权限控制和安全检查

**数据访问层 (Data Access Layer)**:
- 通过Prisma ORM访问数据库
- 数据模型定义和关系管理
- 数据库事务处理

**数据存储层 (Data Storage Layer)**:
- PostgreSQL关系型数据库
- 数据持久化和备份
- 索引优化和性能调优

---

## 3. 技术栈规范

### 3.1 前端技术栈

| 技术组件 | 版本 | 用途 | 说明 |
|---------|------|------|------|
| Next.js | 15.2.4 | 全栈框架 | React全栈开发框架 |
| React | 19.x | UI库 | 用户界面开发 |
| TypeScript | 5.x | 类型系统 | 类型安全开发 |
| Tailwind CSS | 3.4.17 | CSS框架 | 原子化CSS样式 |
| Radix UI | 1.x | 组件库 | 无障碍UI组件 |
| React Hook Form | latest | 表单管理 | 高性能表单处理 |
| Zod | latest | 数据验证 | 类型安全的数据验证 |
| Recharts | 2.15.3 | 图表库 | 数据可视化 |### 3.2 后端技术栈

| 技术组件 | 版本 | 用途 | 说明 |
|---------|------|------|------|
| Next.js API | 15.2.4 | API服务 | 服务端API开发 |
| Prisma | 6.7.0 | ORM框架 | 数据库对象关系映射 |
| NextAuth.js | latest | 认证系统 | 用户认证和会话管理 |
| PostgreSQL | 15 | 数据库 | 关系型数据库 |
| bcryptjs | latest | 密码加密 | 密码哈希处理 |
| nodemailer | latest | 邮件服务 | 邮件发送功能 |

### 3.3 开发工具

| 工具 | 版本 | 用途 | 说明 |
|------|------|------|------|
| Vitest | 3.1.3 | 单元测试 | 快速单元测试框架 |
| Playwright | 1.52.0 | E2E测试 | 端到端测试框架 |
| Husky | 9.0.11 | Git钩子 | 代码提交前检查 |
| TypeDoc | 0.25.12 | 文档生成 | API文档自动生成 |
| Docker | latest | 容器化 | 应用容器化部署 |

---

## 4. 数据库设计

### 4.1 数据库架构

**数据库类型**: PostgreSQL 15  
**字符编码**: UTF-8  
**时区设置**: UTC  
**连接池**: Prisma内置连接池  

### 4.2 核心数据模型

系统包含93个数据表，主要分为以下几个模块：

**用户管理模块**:
- User (用户表)
- Employee (员工表)  
- Role (角色表)
- Permission (权限表)
- UserRole (用户角色关联表)
- RolePermission (角色权限关联表)**产品管理模块**:
- Product (产品表)
- ProductCategory (产品分类表)
- ProductTag (产品标签表)
- ProductTagsOnProducts (产品标签关联表)

**库存管理模块**:
- InventoryItem (库存项目表)
- InventoryTransaction (库存事务表)
- Warehouse (仓库表)

**订单管理模块**:
- Order (订单表)
- OrderItem (订单项目表)
- Customer (客户表)

**财务管理模块**:
- FinanceRecord (财务记录表)
- FinancialAccount (财务账户表)
- FinancialTransaction (财务交易表)

### 4.3 数据库关系设计

**主要外键关系**:
```sql
-- 用户员工关系
User.employeeId → Employee.id (一对一)

-- 产品分类关系  
Product.categoryId → ProductCategory.id (多对一)

-- 订单关系
Order.customerId → Customer.id (多对一)
Order.employeeId → Employee.id (多对一)
OrderItem.orderId → Order.id (多对一)
OrderItem.productId → Product.id (多对一)

-- 库存关系
InventoryItem.productId → Product.id (一对一)
InventoryTransaction.productId → Product.id (多对一)
```

### 4.4 索引设计

**主要索引**:
- 用户邮箱唯一索引: `User.email`
- 产品SKU索引: `Product.sku`
- 订单编号唯一索引: `Order.orderNumber`
- 时间范围查询索引: 各表的`createdAt`字段
- 状态查询索引: 各表的`status`字段---

## 5. API设计规范

### 5.1 RESTful API设计原则

**URL命名规范**:
- 使用名词复数形式: `/api/products`, `/api/orders`
- 层级关系表示: `/api/orders/{id}/items`
- 查询参数: `/api/products?category=jewelry&status=active`

**HTTP方法使用**:
- `GET`: 获取资源
- `POST`: 创建资源
- `PUT`: 完整更新资源
- `PATCH`: 部分更新资源
- `DELETE`: 删除资源

**响应状态码**:
- `200`: 成功
- `201`: 创建成功
- `400`: 请求错误
- `401`: 未认证
- `403`: 无权限
- `404`: 资源不存在
- `500`: 服务器错误

### 5.2 API响应格式

**成功响应**:
```json
{
  "success": true,
  "data": {
    // 实际数据
  },
  "message": "操作成功",
  "timestamp": "2025-06-28T10:30:00Z"
}
```

**错误响应**:
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "数据验证失败",
    "details": ["字段名称为必填项"]
  },
  "timestamp": "2025-06-28T10:30:00Z"
}
```### 5.3 API认证和授权

**认证机制**:
- JWT Token认证
- 会话有效期: 24小时
- 自动刷新机制

**权限控制**:
- 基于角色的访问控制(RBAC)
- API级别权限检查
- 细粒度权限管理

---

## 6. 安全设计

### 6.1 认证安全

**密码安全**:
- bcrypt哈希算法
- 盐值长度: 12位
- 密码复杂度要求

**会话安全**:
- JWT Token签名
- HTTPS传输加密
- 会话超时机制

### 6.2 数据安全

**输入验证**:
- Zod数据验证
- SQL注入防护
- XSS攻击防护

**数据加密**:
- 敏感数据加密存储
- 传输层TLS加密
- 数据库连接加密

### 6.3 访问控制

**权限模型**:
```
用户 → 角色 → 权限 → 资源
User → Role → Permission → Resource
```

**权限粒度**:
- 模块级权限: 如产品管理、订单管理
- 操作级权限: 如查看、创建、编辑、删除
- 数据级权限: 如只能查看自己的数据---

## 7. 部署架构

### 7.1 容器化部署

**Docker配置**:
- 基础镜像: `node:18-alpine`
- 多阶段构建优化
- 生产环境优化

**容器编排**:
```yaml
services:
  app:
    image: linghua-erp:latest
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
    depends_on:
      - db
  
  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=linghua_enamel_gallery
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
```

### 7.2 环境配置

**开发环境**:
- 本地开发服务器
- 热重载支持
- 开发工具集成

**测试环境**:
- 自动化测试
- 持续集成
- 代码质量检查

**生产环境**:
- 性能优化
- 安全加固
- 监控告警---

## 8. 性能设计

### 8.1 前端性能优化

**代码分割**:
- 路由级代码分割
- 组件懒加载
- 动态导入优化

**缓存策略**:
- 浏览器缓存
- CDN缓存
- 服务端缓存

### 8.2 后端性能优化

**数据库优化**:
- 查询优化
- 索引策略
- 连接池管理

**API优化**:
- 响应压缩
- 分页查询
- 批量操作

---

## 9. 监控和日志

### 9.1 系统监控

**性能监控**:
- 响应时间监控
- 资源使用监控
- 错误率监控

**业务监控**:
- 用户行为分析
- 业务指标监控
- 异常检测

### 9.2 日志管理

**日志分类**:
- 访问日志
- 错误日志
- 业务日志
- 安全日志

**日志格式**:
```json
{
  "timestamp": "2025-06-28T10:30:00Z",
  "level": "INFO",
  "module": "order",
  "action": "create",
  "userId": "user123",
  "message": "订单创建成功",
  "details": {}
}
```

---

## 10. 扩展性设计

### 10.1 水平扩展

**应用层扩展**:
- 负载均衡
- 多实例部署
- 会话共享

**数据层扩展**:
- 读写分离
- 分库分表
- 缓存集群

### 10.2 功能扩展

**模块化设计**:
- 插件化架构
- 微服务拆分
- API网关

**集成扩展**:
- 第三方系统集成
- 开放API接口
- Webhook支持

---

**文档维护说明**:
- 本文档随系统版本更新
- 重大架构变更需要更新文档
- 定期审查和优化设计方案