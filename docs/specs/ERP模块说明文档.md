# 聆花ERP系统模块说明文档

## 1. 模块架构概述

### 1.1 模块划分原则
聆花ERP系统采用领域驱动设计(DDD)原则，按业务领域划分模块，每个模块具有明确的边界和职责，模块间通过标准接口进行通信。

### 1.2 模块依赖关系
```
认证模块 (Core)
    ↓
业务模块层 (Business Modules)
├── 产品管理模块
├── 库存管理模块
├── 销售管理模块
├── 采购管理模块
├── 生产管理模块
├── 工坊管理模块
├── 财务管理模块
└── 人事管理模块
    ↓
基础设施层 (Infrastructure)
├── 数据访问模块
├── 通知模块
├── 文件管理模块
└── 报表模块
```

## 2. 核心基础模块

### 2.1 认证与权限模块 (Auth Module)

#### 2.1.1 模块概述
- **模块名称**: `auth`
- **模块路径**: `/app/(auth)`, `/lib/auth-*`, `/components/auth`
- **主要职责**: 用户身份验证、权限控制、会话管理

#### 2.1.2 组件结构
```
auth/
├── components/
│   ├── login-form.tsx          # 登录表单组件
│   ├── user-menu.tsx           # 用户菜单组件
│   └── auth-guard.tsx          # 权限守卫组件
├── lib/
│   ├── auth-helpers.ts         # 认证辅助函数
│   ├── auth-middleware.ts      # 认证中间件
│   └── permissions.ts          # 权限定义
└── pages/
    ├── login/                  # 登录页面
    ├── logout/                 # 登出页面
    └── unauthorized/           # 未授权页面
```

#### 2.1.3 数据模型
- **User**: 用户基础信息
- **Role**: 角色定义
- **Permission**: 权限定义
- **UserRole**: 用户角色关联
- **RolePermission**: 角色权限关联

#### 2.1.4 主要功能
- 用户登录验证
- JWT Token管理
- 权限检查中间件
- 角色权限管理
- 会话状态维护

### 2.2 UI组件模块 (UI Module)

#### 2.2.1 模块概述
- **模块名称**: `ui`
- **模块路径**: `/components/ui`
- **主要职责**: 基础UI组件库、样式系统

#### 2.2.2 组件结构
```
ui/
├── button.tsx                  # 按钮组件
├── input.tsx                   # 输入框组件
├── select.tsx                  # 选择器组件
├── table.tsx                   # 表格组件
├── dialog.tsx                  # 对话框组件
├── form.tsx                    # 表单组件
├── toast.tsx                   # 提示组件
├── loading.tsx                 # 加载组件
├── pagination.tsx              # 分页组件
└── layout/
    ├── header.tsx              # 头部组件
    ├── sidebar.tsx             # 侧边栏组件
    └── footer.tsx              # 底部组件
```

#### 2.2.3 设计系统
- **基于Radix UI**: 无障碍访问支持
- **Tailwind CSS**: 样式系统
- **主题系统**: 支持亮色/暗色主题
- **响应式设计**: 支持多种屏幕尺寸

## 3. 业务功能模块

### 3.1 产品管理模块 (Product Module)

#### 3.1.1 模块概述
- **模块名称**: `product`
- **模块路径**: `/app/(main)/products`, `/components/product`
- **主要职责**: 产品信息管理、分类管理、标签管理

#### 3.1.2 组件结构
```
product/
├── components/
│   ├── product-form.tsx        # 产品表单
│   ├── product-list.tsx        # 产品列表
│   ├── product-card.tsx        # 产品卡片
│   ├── category-tree.tsx       # 分类树
│   ├── tag-manager.tsx         # 标签管理
│   └── product-search.tsx      # 产品搜索
├── pages/
│   ├── page.tsx                # 产品列表页
│   ├── [id]/page.tsx          # 产品详情页
│   └── materials-units/       # 材料单位管理
└── api/
    ├── route.ts                # 产品API
    ├── categories/            # 分类API
    └── tags/                  # 标签API
```

#### 3.1.3 数据模型
- **Product**: 产品基础信息
- **ProductCategory**: 产品分类
- **ProductTag**: 产品标签
- **ProductTagsOnProducts**: 产品标签关联

#### 3.1.4 核心功能
- 产品CRUD操作
- 产品分类管理
- 产品标签系统
- 产品图片管理
- 产品搜索过滤
- 批量操作功能

### 3.2 库存管理模块 (Inventory Module)

#### 3.2.1 模块概述
- **模块名称**: `inventory`
- **模块路径**: `/app/(main)/inventory`, `/components/inventory`
- **主要职责**: 库存监控、出入库管理、仓库管理

#### 3.2.2 组件结构
```
inventory/
├── components/
│   ├── inventory-list.tsx      # 库存列表
│   ├── warehouse-select.tsx    # 仓库选择
│   ├── stock-alert.tsx         # 库存预警
│   ├── transaction-form.tsx    # 库存变动表单
│   └── inventory-chart.tsx     # 库存图表
├── pages/
│   ├── page.tsx                # 库存主页
│   ├── warehouses/            # 仓库管理
│   └── transactions/          # 库存变动
└── api/
    ├── route.ts                # 库存API
    ├── warehouses/            # 仓库API
    └── transactions/          # 变动API
```

#### 3.2.3 数据模型
- **Warehouse**: 仓库信息
- **InventoryItem**: 库存明细
- **InventoryTransaction**: 库存变动记录
- **ChannelInventory**: 渠道库存

#### 3.2.4 核心功能
- 实时库存查询
- 出入库操作
- 库存调拨功能
- 库存盘点管理
- 低库存预警
- 库存分析报表

### 3.3 销售管理模块 (Sales Module)

#### 3.3.1 模块概述
- **模块名称**: `sales`
- **模块路径**: `/app/(main)/sales`, `/components/sales`
- **主要职责**: 订单管理、客户管理、多渠道销售

#### 3.3.2 组件结构
```
sales/
├── components/
│   ├── order-form.tsx          # 订单表单
│   ├── order-list.tsx          # 订单列表
│   ├── customer-select.tsx     # 客户选择
│   ├── sales-chart.tsx         # 销售图表
│   └── payment-form.tsx        # 支付表单
├── pages/
│   ├── page.tsx                # 销售主页
│   ├── orders/                # 订单管理
│   ├── customers/             # 客户管理
│   └── pos/                   # POS销售
└── api/
    ├── orders/                # 订单API
    ├── customers/             # 客户API
    └── pos-sales/             # POS销售API
```

#### 3.3.3 数据模型
- **Order**: 订单信息
- **OrderItem**: 订单明细
- **Customer**: 客户信息
- **GallerySale**: 画廊销售
- **PosSale**: POS销售
- **ChannelSale**: 渠道销售

#### 3.3.4 核心功能
- 订单创建管理
- 客户信息维护
- 多渠道销售支持
- 销售数据统计
- 支付状态跟踪
- 销售报表生成

### 3.4 采购管理模块 (Purchase Module)

#### 3.4.1 模块概述
- **模块名称**: `purchase`
- **模块路径**: `/app/(main)/purchase`, `/components/purchase`
- **主要职责**: 采购订单管理、供应商管理、审批流程

#### 3.4.2 组件结构
```
purchase/
├── components/
│   ├── purchase-order-form.tsx # 采购订单表单
│   ├── supplier-select.tsx     # 供应商选择
│   ├── approval-workflow.tsx   # 审批流程
│   ├── receiving-form.tsx      # 收货表单
│   └── purchase-analytics.tsx  # 采购分析
├── pages/
│   ├── page.tsx                # 采购主页
│   ├── orders/                # 采购订单
│   ├── suppliers/             # 供应商管理
│   └── approvals/             # 审批管理
└── api/
    ├── purchase-orders/       # 采购订单API
    ├── suppliers/             # 供应商API
    └── approvals/             # 审批API
```

#### 3.4.3 数据模型
- **PurchaseOrder**: 采购订单
- **PurchaseOrderItem**: 采购明细
- **Supplier**: 供应商信息
- **PurchaseOrderApproval**: 采购审批
- **PurchaseOrderReceiving**: 收货记录

#### 3.4.4 核心功能
- 采购订单管理
- 供应商档案管理
- 多级审批流程
- 收货验收管理
- 采购成本分析
- 供应商评价系统

### 3.5 生产管理模块 (Production Module)

#### 3.5.1 模块概述
- **模块名称**: `production`
- **模块路径**: `/app/(main)/production`, `/components/production`
- **主要职责**: 生产订单管理、质量控制、生产基地管理

#### 3.5.2 组件结构
```
production/
├── components/
│   ├── production-order-form.tsx # 生产订单表单
│   ├── quality-form.tsx        # 质量检查表单
│   ├── production-chart.tsx    # 生产图表
│   ├── base-select.tsx         # 生产基地选择
│   └── shipping-form.tsx       # 发货表单
├── pages/
│   ├── page.tsx                # 生产主页
│   ├── orders/                # 生产订单
│   ├── quality/               # 质量管理
│   └── bases/                 # 生产基地
└── api/
    ├── production-orders/     # 生产订单API
    ├── quality/               # 质量API
    └── bases/                 # 生产基地API
```

#### 3.5.3 数据模型
- **ProductionBase**: 生产基地
- **ProductionOrder**: 生产订单
- **ProductionOrderItem**: 生产明细
- **QualityRecord**: 质量记录
- **ShippingRecord**: 发货记录

#### 3.5.4 核心功能
- 生产计划制定
- 生产订单跟踪
- 质量检查管理
- 生产基地管理
- 发货物流管理
- 生产效率分析

### 3.6 工坊管理模块 (Workshop Module)

#### 3.6.1 模块概述
- **模块名称**: `workshop`
- **模块路径**: `/app/(main)/workshops`, `/components/workshop`
- **主要职责**: 工坊课程管理、教师管理、预约系统

#### 3.6.2 组件结构
```
workshop/
├── components/
│   ├── workshop-form.tsx       # 工坊表单
│   ├── activity-form.tsx       # 活动表单
│   ├── teacher-select.tsx      # 教师选择
│   ├── schedule-calendar.tsx   # 排期日历
│   └── workshop-analytics.tsx  # 工坊分析
├── pages/
│   ├── page.tsx                # 工坊主页
│   ├── activities/            # 活动管理
│   ├── team/                  # 团队管理
│   └── schedule/              # 排期管理
└── api/
    ├── workshops/             # 工坊API
    ├── activities/            # 活动API
    └── team-members/          # 团队API
```

#### 3.6.3 数据模型
- **Workshop**: 工坊记录
- **WorkshopActivity**: 工坊活动
- **WorkshopTeamMember**: 团队成员
- **WorkshopPrice**: 价格管理
- **WorkshopServiceItem**: 服务项目

#### 3.6.4 核心功能
- 工坊课程管理
- 教师团队管理
- 课程预约系统
- 排期安排管理
- 工坊结算系统
- 教学效果分析

### 3.7 财务管理模块 (Finance Module)

#### 3.7.1 模块概述
- **模块名称**: `finance`
- **模块路径**: `/app/(main)/finance`, `/components/finance`
- **主要职责**: 财务记录管理、报表生成、成本核算

#### 3.7.2 组件结构
```
finance/
├── components/
│   ├── transaction-form.tsx    # 交易表单
│   ├── account-select.tsx      # 账户选择
│   ├── finance-chart.tsx       # 财务图表
│   ├── report-generator.tsx    # 报表生成器
│   └── cost-calculator.tsx     # 成本计算器
├── pages/
│   ├── page.tsx                # 财务主页
│   ├── accounts/              # 账户管理
│   ├── transactions/          # 交易记录
│   └── reports/               # 财务报表
└── api/
    ├── finance/               # 财务API
    ├── accounts/              # 账户API
    └── reports/               # 报表API
```

#### 3.7.3 数据模型
- **FinancialAccount**: 财务账户
- **FinancialTransaction**: 财务交易
- **FinancialCategory**: 财务分类
- **FinanceRecord**: 财务记录

#### 3.7.4 核心功能
- 财务账户管理
- 收支记录管理
- 财务报表生成
- 成本核算分析
- 利润分析统计
- 财务数据导出

### 3.8 人事管理模块 (Employee Module)

#### 3.8.1 模块概述
- **模块名称**: `employee`
- **模块路径**: `/app/(main)/employees`, `/components/employee`
- **主要职责**: 员工档案管理、排班系统、薪资管理

#### 3.8.2 组件结构
```
employee/
├── components/
│   ├── employee-form.tsx       # 员工表单
│   ├── schedule-form.tsx       # 排班表单
│   ├── salary-calculator.tsx   # 薪资计算器
│   ├── attendance-chart.tsx    # 考勤图表
│   └── employee-card.tsx       # 员工卡片
├── pages/
│   ├── page.tsx                # 员工主页
│   ├── schedules/             # 排班管理
│   ├── payroll/               # 薪资管理
│   └── attendance/            # 考勤管理
└── api/
    ├── employees/             # 员工API
    ├── schedules/             # 排班API
    └── payroll/               # 薪资API
```

#### 3.8.3 数据模型
- **Employee**: 员工信息
- **Schedule**: 排班记录
- **SalaryRecord**: 薪资记录
- **SalaryAdjustment**: 薪资调整
- **PieceWork**: 计件工作

#### 3.8.4 核心功能
- 员工档案管理
- 排班计划制定
- 薪资自动计算
- 考勤数据统计
- 绩效评价系统
- 人事报表生成

## 4. 支撑服务模块

### 4.1 通知消息模块 (Notification Module)

#### 4.1.1 模块概述
- **模块名称**: `notification`
- **模块路径**: `/components/notifications`, `/api/notifications`
- **主要职责**: 系统通知、消息推送、提醒管理

#### 4.1.2 功能特性
- 实时消息推送
- 多种通知类型
- 消息模板管理
- 通知历史记录
- 个性化通知设置

### 4.2 文件管理模块 (File Module)

#### 4.2.1 模块概述
- **模块名称**: `file`
- **模块路径**: `/api/upload`, `/lib/file-utils`
- **主要职责**: 文件上传、存储、访问管理

#### 4.2.2 功能特性
- 多格式文件上传
- 图片压缩优化
- 文件安全扫描
- 云存储集成
- 文件访问控制

### 4.3 报表导出模块 (Export Module)

#### 4.3.1 模块概述
- **模块名称**: `export`
- **模块路径**: `/api/export`, `/lib/export-utils`
- **主要职责**: 数据导出、报表生成、格式转换

#### 4.3.2 功能特性
- Excel/PDF导出
- 自定义报表模板
- 批量数据导出
- 报表邮件发送
- 导出权限控制

### 4.4 搜索模块 (Search Module)

#### 4.4.1 模块概述
- **模块名称**: `search`
- **模块路径**: `/api/search`, `/lib/fuzzy-search`
- **主要职责**: 全文搜索、模糊搜索、搜索优化

#### 4.4.2 功能特性
- 全局搜索功能
- 模糊匹配算法
- 搜索结果排序
- 搜索历史记录
- 搜索性能优化

## 5. 移动端模块

### 5.1 移动端适配模块 (Mobile Module)

#### 5.1.1 模块概述
- **模块名称**: `mobile`
- **模块路径**: `/app/(mobile)`, `/components/mobile`
- **主要职责**: 移动端UI适配、触摸交互、离线支持

#### 5.1.2 组件结构
```
mobile/
├── layout/
│   ├── mobile-header.tsx      # 移动端头部
│   ├── mobile-nav.tsx         # 移动端导航
│   └── mobile-footer.tsx      # 移动端底部
├── components/
│   ├── mobile-table.tsx       # 移动端表格
│   ├── mobile-form.tsx        # 移动端表单
│   └── mobile-card.tsx        # 移动端卡片
└── pages/
    ├── dashboard/             # 移动端仪表板
    ├── products/              # 移动端产品
    └── sales/                 # 移动端销售
```

#### 5.1.3 功能特性
- 响应式设计
- 触摸友好界面
- 手势操作支持
- 离线数据缓存
- PWA支持

## 6. 系统管理模块

### 6.1 系统设置模块 (Settings Module)

#### 6.1.1 模块概述
- **模块名称**: `settings`
- **模块路径**: `/app/(main)/settings`, `/components/settings`
- **主要职责**: 系统配置、参数管理、用户设置

#### 6.1.2 功能特性
- 系统参数配置
- 业务规则设置
- 数据字典管理
- 用户偏好设置
- 系统性能监控

### 6.2 日志监控模块 (Monitoring Module)

#### 6.2.1 模块概述
- **模块名称**: `monitoring`
- **模块路径**: `/app/admin/monitoring`, `/components/monitoring`
- **主要职责**: 系统监控、日志管理、性能分析

#### 6.2.2 功能特性
- 系统性能监控
- 错误日志收集
- 用户行为分析
- API调用统计
- 系统告警通知

### 6.3 数据备份模块 (Backup Module)

#### 6.3.1 模块概述
- **模块名称**: `backup`
- **模块路径**: `/app/settings/backup`, `/api/backup`
- **主要职责**: 数据备份、恢复管理、备份策略

#### 6.3.2 功能特性
- 自动备份调度
- 增量备份支持
- 备份数据验证
- 快速数据恢复
- 备份存储管理

## 7. 模块间通信

### 7.1 接口设计规范
- **RESTful API**: 统一的API设计风格
- **数据格式**: JSON格式数据交换
- **错误处理**: 统一的错误码和消息
- **版本控制**: API版本管理策略

### 7.2 事件通信机制
- **服务器事件**: Server-Sent Events
- **实时通信**: WebSocket连接
- **消息队列**: 异步消息处理
- **事件总线**: 模块间事件通知

### 7.3 数据共享机制
- **共享状态**: React Context
- **缓存机制**: Redis缓存
- **数据同步**: 实时数据更新
- **事务管理**: 数据一致性保证

## 8. 模块扩展指南

### 8.1 新模块开发流程
1. **需求分析** → 确定模块功能和边界
2. **设计阶段** → 定义数据模型和接口
3. **开发阶段** → 按照规范开发组件
4. **测试阶段** → 单元测试和集成测试
5. **部署阶段** → 模块部署和配置

### 8.2 模块开发规范
- **命名规范**: 统一的文件和变量命名
- **代码风格**: ESLint和Prettier配置
- **文档规范**: JSDoc注释和README
- **测试规范**: 单元测试覆盖率要求
- **安全规范**: 安全编码规范

### 8.3 模块维护指南
- **版本管理**: 语义化版本控制
- **依赖管理**: 依赖版本锁定
- **性能优化**: 代码分割和懒加载
- **监控报警**: 模块性能监控
- **文档更新**: 及时更新文档

---

*本文档版本: v1.0*  
*最后更新时间: 2025年6月28日*  
*维护团队: 聆花ERP开发团队*