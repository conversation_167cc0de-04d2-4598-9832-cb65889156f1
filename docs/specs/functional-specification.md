# 聆花珐琅馆ERP系统功能规范

**文档版本**: v1.0  
**创建日期**: 2025-06-28  
**最后更新**: 2025-06-28  
**文档状态**: 正式版  
**维护人员**: 产品团队  

## 文档概述

本文档详细描述了聆花珐琅馆ERP系统的所有功能模块、用户角色、业务流程、操作规范和业务规则，为产品开发、测试和用户培训提供功能指导。

## 目录

1. [系统功能概述](#1-系统功能概述)
2. [用户角色和权限](#2-用户角色和权限)
3. [核心业务流程](#3-核心业务流程)
4. [功能模块详述](#4-功能模块详述)
5. [用户界面规范](#5-用户界面规范)
6. [业务规则](#6-业务规则)
7. [数据流转](#7-数据流转)
8. [报表和分析](#8-报表和分析)
9. [系统集成](#9-系统集成)
10. [移动端功能](#10-移动端功能)

---

## 1. 系统功能概述

### 1.1 系统定位

聆花珐琅馆ERP系统是专为珐琅艺术品行业设计的综合管理平台，涵盖从产品设计、生产制作、库存管理到销售分析的完整业务链条。

### 1.2 核心功能模块

系统包含13个核心功能模块：

| 模块名称 | 功能描述 | 主要用户 |
|---------|----------|----------|
| 用户管理 | 用户账户、角色权限管理 | 系统管理员 |
| 产品管理 | 产品信息、分类、库存管理 | 产品经理、库管员 |
| 订单管理 | 销售订单、客户管理 | 销售人员、客服 |
| 采购管理 | 供应商、采购订单管理 | 采购员、财务 |
| 生产管理 | 生产计划、质量控制 | 生产经理、质检员 |
| 库存管理 | 库存跟踪、调拨管理 | 库管员、采购员 |
| 财务管理 | 收支记录、财务报表 | 财务人员、管理层 |
| 人力资源 | 员工信息、薪资管理 | HR、管理层 |
| 工坊管理 | 课程安排、学员管理 | 工坊老师、管理员 |
| 咖啡店POS | 销售录入、收银管理 | 店员、店长 |
| 渠道管理 | 多渠道销售管理 | 渠道经理、销售 |
| 报表分析 | 数据分析、业务报表 | 管理层、分析师 |
| 系统管理 | 系统配置、日志管理 | 系统管理员 |---

## 2. 用户角色和权限

### 2.1 角色定义

**超级管理员 (Super Admin)**:
- 系统最高权限
- 可以管理所有功能模块
- 用户和权限管理
- 系统配置和维护

**管理员 (Admin)**:
- 业务管理权限
- 可以查看所有业务数据
- 审批和决策权限
- 报表查看权限

**财务人员 (Finance)**:
- 财务数据管理
- 成本核算权限
- 财务报表查看
- 收支记录管理

**销售人员 (Sales)**:
- 订单创建和管理
- 客户信息管理
- 销售数据查看
- 产品价格查询

**采购员 (Purchaser)**:
- 采购订单管理
- 供应商信息管理
- 库存补货申请
- 采购成本查看

**生产人员 (Production)**:
- 生产计划查看
- 生产进度更新
- 质量检验记录
- 物料需求申请

**库管员 (Warehouse)**:
- 库存数据管理
- 入库出库操作
- 库存盘点记录
- 库存报表查看

**店员 (Staff)**:
- 咖啡店销售录入
- 基础产品信息查看
- 个人销售数据查看
- 考勤打卡功能### 2.2 权限矩阵

| 功能模块 | 超级管理员 | 管理员 | 财务 | 销售 | 采购 | 生产 | 库管 | 店员 |
|---------|-----------|--------|------|------|------|------|------|------|
| 用户管理 | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |
| 产品管理 | ✅ | ✅ | 👁️ | 👁️ | 👁️ | 👁️ | ✅ | 👁️ |
| 订单管理 | ✅ | ✅ | 👁️ | ✅ | 👁️ | 👁️ | 👁️ | 👁️ |
| 采购管理 | ✅ | ✅ | ✅ | ❌ | ✅ | 👁️ | 👁️ | ❌ |
| 生产管理 | ✅ | ✅ | 👁️ | 👁️ | 👁️ | ✅ | 👁️ | ❌ |
| 库存管理 | ✅ | ✅ | 👁️ | 👁️ | ✅ | 👁️ | ✅ | ❌ |
| 财务管理 | ✅ | ✅ | ✅ | 👁️ | 👁️ | ❌ | ❌ | ❌ |
| 人力资源 | ✅ | ✅ | ✅ | 👁️ | 👁️ | 👁️ | 👁️ | 👁️ |
| 工坊管理 | ✅ | ✅ | 👁️ | 👁️ | ❌ | ❌ | ❌ | ❌ |
| 咖啡店POS | ✅ | ✅ | 👁️ | ✅ | ❌ | ❌ | ❌ | ✅ |
| 渠道管理 | ✅ | ✅ | 👁️ | ✅ | ❌ | ❌ | ❌ | ❌ |
| 报表分析 | ✅ | ✅ | ✅ | 👁️ | 👁️ | 👁️ | 👁️ | 👁️ |
| 系统管理 | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |

**图例**: ✅ 完全权限 | 👁️ 只读权限 | ❌ 无权限

---

## 3. 核心业务流程

### 3.1 订单处理流程

```
客户咨询 → 产品选择 → 价格确认 → 订单创建 → 库存检查
    ↓
订单确认 → 生产安排 → 进度跟踪 → 质量检验 → 发货配送
    ↓
客户确认 → 订单完成 → 财务结算 → 售后服务
```

**详细步骤**:
1. **订单创建**: 销售人员录入客户需求和产品信息
2. **库存检查**: 系统自动检查产品库存状态
3. **订单确认**: 客户确认订单详情和交期
4. **生产安排**: 根据库存情况安排生产或直接发货
5. **进度跟踪**: 实时更新订单状态和生产进度
6. **质量检验**: 产品完成后进行质量检查
7. **发货配送**: 安排物流配送给客户
8. **订单完成**: 客户确认收货，订单状态更新为完成
9. **财务结算**: 更新财务记录，计算销售提成### 3.2 采购管理流程

```
库存预警 → 采购需求 → 供应商选择 → 采购订单 → 订单审批
    ↓
供应商确认 → 货物配送 → 入库检验 → 库存更新 → 财务结算
```

### 3.3 生产管理流程

```
生产计划 → 物料准备 → 生产安排 → 进度跟踪 → 质量检验
    ↓
成品入库 → 成本核算 → 库存更新 → 生产报告
```

### 3.4 薪资计算流程

```
考勤统计 → 基本工资 → 销售提成 → 计件工资 → 薪资汇总
    ↓
薪资审核 → 薪资发放 → 财务记录 → 薪资报表
```

---

## 4. 功能模块详述

### 4.1 产品管理模块

**主要功能**:
- 产品信息管理 (名称、价格、材料、规格)
- 产品分类管理 (多级分类体系)
- 产品标签管理 (便于搜索和筛选)
- 产品图片管理 (多图片上传和展示)
- 产品库存管理 (实时库存数量)

**操作流程**:
1. **新增产品**: 填写产品基本信息 → 选择分类 → 上传图片 → 设置价格 → 保存
2. **编辑产品**: 查找产品 → 修改信息 → 保存更改
3. **删除产品**: 检查是否有关联数据 → 确认删除 → 更新相关记录
4. **批量操作**: 选择多个产品 → 批量修改价格/分类 → 确认操作

**业务规则**:
- 产品名称不能重复
- 价格必须大于0
- 删除产品前需要检查是否有未完成的订单
- 库存数量不能为负数### 4.2 订单管理模块

**主要功能**:
- 订单创建和编辑
- 订单状态跟踪
- 客户信息管理
- 订单项目管理
- 支付状态管理

**订单状态定义**:
- `pending`: 待确认
- `confirmed`: 已确认
- `production`: 生产中
- `shipped`: 已发货
- `completed`: 已完成
- `cancelled`: 已取消

**操作流程**:
1. **创建订单**: 选择客户 → 添加产品 → 设置数量和价格 → 计算总金额 → 保存订单
2. **订单确认**: 检查库存 → 确认交期 → 更新状态为已确认
3. **订单发货**: 准备货物 → 选择物流 → 更新状态为已发货
4. **订单完成**: 客户确认收货 → 更新状态为已完成 → 生成财务记录

### 4.3 库存管理模块

**主要功能**:
- 库存实时跟踪
- 入库出库管理
- 库存调拨
- 库存盘点
- 库存预警

**库存事务类型**:
- `IN`: 入库 (采购入库、生产入库、调拨入库)
- `OUT`: 出库 (销售出库、生产领料、调拨出库)
- `ADJUST`: 调整 (盘点调整、损耗调整)

**操作流程**:
1. **入库操作**: 选择产品 → 输入数量 → 选择入库类型 → 填写备注 → 确认入库
2. **出库操作**: 选择产品 → 检查库存 → 输入出库数量 → 选择出库原因 → 确认出库
3. **库存盘点**: 选择盘点范围 → 录入实际数量 → 计算差异 → 生成调整记录
4. **库存预警**: 设置预警阈值 → 系统自动检查 → 生成预警通知### 4.4 财务管理模块

**主要功能**:
- 收支记录管理
- 财务分类管理
- 成本核算
- 利润分析
- 财务报表生成

**财务记录类型**:
- `income`: 收入 (销售收入、其他收入)
- `expense`: 支出 (采购支出、运营支出)
- `transfer`: 转账 (内部资金调拨)

**操作流程**:
1. **记录收入**: 选择收入类型 → 输入金额 → 选择客户/项目 → 填写说明 → 保存记录
2. **记录支出**: 选择支出类型 → 输入金额 → 选择供应商/项目 → 上传凭证 → 保存记录
3. **成本核算**: 选择产品/项目 → 汇总相关成本 → 计算成本率 → 生成成本报告
4. **生成报表**: 选择报表类型 → 设置时间范围 → 选择筛选条件 → 生成并导出

---

## 5. 用户界面规范

### 5.1 界面布局

**主要布局结构**:
- 顶部导航栏: Logo、用户信息、快捷操作
- 左侧菜单栏: 功能模块导航
- 主内容区: 页面主要内容
- 底部状态栏: 版本信息、在线状态

**响应式设计**:
- 桌面端: 1200px以上，完整功能展示
- 平板端: 768px-1199px，适配触摸操作
- 移动端: 767px以下，简化界面布局

### 5.2 交互规范

**按钮规范**:
- 主要操作: 蓝色按钮 (保存、确认、提交)
- 次要操作: 灰色按钮 (取消、返回)
- 危险操作: 红色按钮 (删除、作废)

**表单规范**:
- 必填字段标红星号 (*)
- 实时验证和错误提示
- 统一的输入框样式
- 清晰的标签和说明---

## 6. 业务规则

### 6.1 数据验证规则

**产品管理**:
- 产品名称: 必填，长度1-100字符，不能重复
- 产品价格: 必填，数值类型，大于等于0
- 产品SKU: 可选，长度1-50字符，不能重复
- 库存数量: 必填，整数类型，大于等于0

**订单管理**:
- 订单编号: 系统自动生成，格式: ORD-YYYYMMDD-XXXX
- 客户信息: 必填，必须是已存在的客户
- 订单金额: 自动计算，等于所有订单项金额之和
- 订单状态: 只能按照预定义流程变更

**库存管理**:
- 库存数量不能为负数
- 出库数量不能大于当前库存
- 库存调整需要填写调整原因
- 库存盘点需要管理员权限

### 6.2 业务流程规则

**订单审批规则**:
- 订单金额 < 1000元: 自动审批
- 订单金额 1000-5000元: 需要主管审批
- 订单金额 > 5000元: 需要经理审批

**采购审批规则**:
- 采购金额 < 500元: 自动审批
- 采购金额 500-2000元: 需要主管审批
- 采购金额 > 2000元: 需要经理审批

**薪资计算规则**:
- 基本工资 = 日薪 × 出勤天数
- 销售提成 = 销售金额 × 提成比例
- 计件工资 = 完成数量 × 单价
- 总薪资 = 基本工资 + 销售提成 + 计件工资

---

## 7. 数据流转

### 7.1 订单数据流

```
订单创建 → 库存检查 → 生产计划 → 财务记录
    ↓         ↓         ↓         ↓
客户数据   产品数据   生产数据   收入数据
```

### 7.2 库存数据流

```
采购入库 → 库存增加 → 销售出库 → 库存减少
    ↓         ↓         ↓         ↓
采购数据   库存数据   销售数据   成本数据
```

---

## 8. 报表和分析

### 8.1 销售报表

**销售汇总报表**:
- 按时间维度: 日报、周报、月报、年报
- 按产品维度: 产品销量、销售额排行
- 按客户维度: 客户购买频次、金额统计
- 按员工维度: 员工销售业绩、提成统计

**销售趋势分析**:
- 销售额趋势图
- 产品销量趋势
- 客户增长趋势
- 季节性分析

### 8.2 库存报表

**库存状态报表**:
- 当前库存汇总
- 库存周转率分析
- 滞销产品统计
- 库存预警报告

### 8.3 财务报表

**收支报表**:
- 收入支出汇总
- 利润分析报告
- 成本结构分析
- 现金流量表

---

## 9. 系统集成

### 9.1 内部集成

**模块间数据同步**:
- 订单系统 ↔ 库存系统
- 销售系统 ↔ 财务系统
- 生产系统 ↔ 采购系统
- 人事系统 ↔ 财务系统

### 9.2 外部集成

**第三方系统对接**:
- 物流系统API对接
- 支付系统集成
- 邮件系统集成
- 短信通知服务

---

## 10. 移动端功能

### 10.1 移动端适配

**主要功能**:
- 产品查询和浏览
- 订单状态查看
- 库存快速查询
- 销售数据录入
- 考勤打卡功能

**界面优化**:
- 触摸友好的按钮设计
- 简化的操作流程
- 快速搜索功能
- 离线数据缓存

---

**文档维护说明**:
- 功能变更需要及时更新文档
- 定期收集用户反馈优化功能
- 新功能上线前需要更新相关文档