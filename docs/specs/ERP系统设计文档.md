# 聆花ERP系统设计文档

## 1. 系统概述

### 1.1 项目简介
聆花ERP系统是一个基于Next.js 15和Prisma的现代化企业资源规划系统，专为聆花琪彩定制手工艺品业务设计。系统集成了生产管理、销售管理、库存管理、财务管理、员工管理等核心业务模块。

### 1.2 技术架构
- **前端框架**: Next.js 15 (React 19)
- **数据库**: PostgreSQL with Prisma ORM
- **UI组件库**: Radix UI + Tailwind CSS
- **状态管理**: React Server Components + Server Actions
- **认证系统**: NextAuth.js
- **部署环境**: Docker容器化部署

### 1.3 系统特点
- 现代化响应式设计，支持桌面端和移动端
- 模块化架构，易于扩展和维护
- 实时数据同步和通知系统
- 完整的权限管理和安全机制
- 多渠道销售管理
- 智能报表和数据分析

## 2. 系统架构设计

### 2.1 技术栈
```
┌─────────────────┐
│   前端层 (UI)    │
├─────────────────┤
│ Next.js 15      │
│ React 19        │
│ Tailwind CSS    │
│ Radix UI        │
└─────────────────┘
        ↕
┌─────────────────┐
│   应用层 (API)   │
├─────────────────┤
│ Server Actions  │
│ API Routes      │
│ NextAuth.js     │
└─────────────────┘
        ↕
┌─────────────────┐
│   数据层 (DB)    │
├─────────────────┤
│ Prisma ORM      │
│ PostgreSQL      │
└─────────────────┘
```

### 2.2 核心架构模式
- **MVC模式**: Model(Prisma Schema) + View(React Components) + Controller(Server Actions)
- **模块化设计**: 按业务领域划分模块
- **事件驱动**: 异步处理和实时通知
- **RESTful API**: 标准化接口设计

### 2.3 目录结构
```
聆花ERP/
├── app/                    # Next.js应用目录
│   ├── (auth)/            # 认证相关页面
│   ├── (main)/            # 主要业务页面
│   ├── (mobile)/          # 移动端页面
│   └── api/               # API路由
├── components/            # React组件
│   ├── ui/               # 基础UI组件
│   ├── product/          # 产品相关组件
│   ├── sales/            # 销售相关组件
│   └── ...               # 其他业务组件
├── lib/                  # 工具库和配置
├── prisma/               # 数据库架构
└── docs/                 # 文档
```

## 3. 数据库设计

### 3.1 核心实体关系图
```
用户(User) ←→ 员工(Employee)
    ↓
角色权限(Role/Permission)

产品(Product) ←→ 产品分类(ProductCategory)
    ↓              ↓
库存(Inventory) → 渠道库存(ChannelInventory)
    ↓
销售订单(Order/GallerySale/PosSale)
    ↓
财务记录(FinanceRecord)

生产基地(ProductionBase) → 生产订单(ProductionOrder)
    ↓
质量记录(QualityRecord)

供应商(Supplier) → 采购订单(PurchaseOrder)
    ↓
采购收货(PurchaseOrderReceiving)

工坊(Workshop) → 工坊活动(WorkshopActivity)
    ↓
员工排班(Schedule)
```

### 3.2 核心表设计说明

#### 用户与权限模块
- **User**: 系统用户表，支持多角色
- **Employee**: 员工信息表，与User关联
- **Role/Permission**: 角色权限管理
- **UserSettings**: 用户个性化设置

#### 产品管理模块
- **Product**: 产品基础信息
- **ProductCategory**: 产品分类层级结构
- **ProductTag**: 产品标签系统
- **Artwork**: 艺术品特殊管理

#### 库存管理模块
- **Warehouse**: 仓库管理
- **InventoryItem**: 库存明细
- **InventoryTransaction**: 库存变动记录
- **ChannelInventory**: 渠道库存分配

#### 销售管理模块
- **Order**: 标准订单管理
- **GallerySale**: 画廊销售
- **PosSale**: POS销售
- **CoffeeShopSale**: 咖啡店销售
- **ChannelSale**: 渠道销售

#### 采购管理模块
- **Supplier**: 供应商信息
- **PurchaseOrder**: 采购订单
- **PurchaseOrderApproval**: 采购审批流程
- **PurchaseOrderReceiving**: 收货管理

#### 生产管理模块
- **ProductionBase**: 生产基地
- **ProductionOrder**: 生产订单
- **QualityRecord**: 质量检查记录
- **ShippingRecord**: 发货记录

#### 工坊管理模块
- **Workshop**: 工坊课程记录
- **WorkshopActivity**: 课程活动定义
- **WorkshopTeamMember**: 团队成员管理
- **WorkshopPrice**: 渠道定价

#### 财务管理模块
- **FinanceRecord**: 财务记录
- **FinancialAccount**: 账户管理
- **FinancialTransaction**: 交易记录
- **ChannelSettlement**: 渠道结算

#### 人事管理模块
- **Schedule**: 员工排班
- **SalaryRecord**: 薪资记录
- **PieceWork**: 计件工作
- **SalaryAdjustment**: 薪资调整

## 4. 业务流程设计

### 4.1 销售流程
```
客户询价 → 报价确认 → 订单创建 → 库存检查 → 
生产安排 → 质量检查 → 发货配送 → 收款结算
```

### 4.2 采购流程
```
需求申请 → 供应商询价 → 采购审批 → 订单下达 → 
货物接收 → 质量检验 → 入库管理 → 财务结算
```

### 4.3 生产流程
```
生产计划 → 原料准备 → 生产安排 → 过程监控 → 
质量检查 → 成品入库 → 生产结算
```

### 4.4 工坊流程
```
课程发布 → 客户预约 → 排期安排 → 教师分配 → 
课程执行 → 学员评价 → 费用结算
```

## 5. 模块设计

### 5.1 认证授权模块
- **功能**: 用户登录、权限验证、会话管理
- **技术**: NextAuth.js + JWT
- **特性**: 支持多种登录方式、细粒度权限控制

### 5.2 产品管理模块
- **功能**: 产品CRUD、分类管理、库存追踪
- **特性**: 支持多规格、多单位、批次管理

### 5.3 销售管理模块
- **功能**: 订单管理、多渠道销售、客户管理
- **特性**: 支持POS、在线、渠道等多种销售方式

### 5.4 库存管理模块
- **功能**: 库存监控、出入库管理、盘点功能
- **特性**: 实时库存同步、低库存预警

### 5.5 采购管理模块
- **功能**: 采购订单、供应商管理、审批流程
- **特性**: 多级审批、自动化流程

### 5.6 生产管理模块
- **功能**: 生产计划、工艺管理、质量控制
- **特性**: 全程可追溯、质量数据分析

### 5.7 财务管理模块
- **功能**: 账目管理、报表生成、成本分析
- **特性**: 多维度财务分析、自动化计算

### 5.8 人事管理模块
- **功能**: 员工管理、排班系统、薪资计算
- **特性**: 灵活排班、自动薪资计算

## 6. 安全设计

### 6.1 认证安全
- JWT Token机制
- 密码加密存储
- 会话超时管理
- 多因素认证支持

### 6.2 权限控制
- 基于角色的访问控制(RBAC)
- 页面级权限验证
- API接口权限控制
- 数据级权限隔离

### 6.3 数据安全
- 数据库连接加密
- 敏感数据脱敏
- 操作日志记录
- 定期备份机制

### 6.4 接口安全
- API访问限流
- 请求参数验证
- SQL注入防护
- XSS攻击防护

## 7. 性能设计

### 7.1 前端性能
- 组件懒加载
- 图片优化压缩
- 代码分割打包
- 缓存策略优化

### 7.2 后端性能
- 数据库查询优化
- 索引策略设计
- 连接池管理
- 缓存机制

### 7.3 系统性能
- 负载均衡配置
- CDN加速
- 数据库读写分离
- 异步任务处理

## 8. 扩展性设计

### 8.1 微服务化
- 模块独立部署
- 服务间通信
- 统一网关管理
- 服务发现机制

### 8.2 多租户支持
- 数据隔离设计
- 配置独立管理
- 资源弹性扩容
- 计费系统集成

### 8.3 国际化支持
- 多语言界面
- 多币种计算
- 本地化配置
- 时区处理

## 9. 监控与运维

### 9.1 系统监控
- 应用性能监控(APM)
- 数据库性能监控
- 服务器资源监控
- 用户行为分析

### 9.2 日志管理
- 结构化日志记录
- 分级日志管理
- 日志分析工具
- 告警机制

### 9.3 备份恢复
- 自动化备份策略
- 多地备份存储
- 快速恢复机制
- 数据一致性验证

## 10. 部署架构

### 10.1 容器化部署
```
Docker容器
├── Web应用容器 (Next.js)
├── 数据库容器 (PostgreSQL)
├── Redis缓存容器
└── Nginx代理容器
```

### 10.2 环境配置
- **开发环境**: 本地Docker Compose
- **测试环境**: 单节点部署
- **生产环境**: 集群化部署

### 10.3 CI/CD流程
```
代码提交 → 自动测试 → 构建镜像 → 
部署测试 → 审核发布 → 生产部署
```

---

*本文档版本: v1.0*  
*最后更新时间: 2025年6月28日*  
*维护团队: 聆花ERP开发团队*