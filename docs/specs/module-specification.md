# 聆花珐琅馆ERP系统模块规范

**文档版本**: v1.0  
**创建日期**: 2025-06-28  
**最后更新**: 2025-06-28  
**文档状态**: 正式版  
**维护人员**: 开发团队  

## 文档概述

本文档详细描述了聆花珐琅馆ERP系统的代码模块结构、API接口、数据模型、组件使用说明和开发指南，为开发人员提供技术实现参考。

## 目录

1. [代码模块结构](#1-代码模块结构)
2. [API接口文档](#2-api接口文档)
3. [数据模型说明](#3-数据模型说明)
4. [前端组件库](#4-前端组件库)
5. [业务逻辑层](#5-业务逻辑层)
6. [工具函数库](#6-工具函数库)
7. [中间件系统](#7-中间件系统)
8. [配置管理](#8-配置管理)
9. [测试规范](#9-测试规范)
10. [开发指南](#10-开发指南)

---

## 1. 代码模块结构

### 1.1 项目目录结构

```
src/
├── app/                    # Next.js App Router
│   ├── (main)/            # 主应用页面
│   │   ├── products/      # 产品管理页面
│   │   ├── orders/        # 订单管理页面
│   │   ├── inventory/     # 库存管理页面
│   │   ├── finance/       # 财务管理页面
│   │   └── reports/       # 报表分析页面
│   ├── (mobile)/          # 移动端页面
│   ├── api/               # API路由
│   │   ├── products/      # 产品相关API
│   │   ├── orders/        # 订单相关API
│   │   ├── inventory/     # 库存相关API
│   │   ├── finance/       # 财务相关API
│   │   └── auth/          # 认证相关API
│   └── globals.css        # 全局样式
├── components/            # React组件库
│   ├── ui/               # 基础UI组件
│   ├── forms/            # 表单组件
│   ├── charts/           # 图表组件
│   └── layout/           # 布局组件
├── lib/                  # 核心业务逻辑
│   ├── actions/          # 服务端操作
│   ├── services/         # 业务服务
│   ├── utils/            # 工具函数
│   ├── hooks/            # 自定义Hook
│   └── types/            # TypeScript类型定义
├── prisma/               # 数据库相关
│   ├── schema.prisma     # 数据模型定义
│   └── migrations/       # 数据库迁移
└── public/               # 静态资源
    ├── images/           # 图片资源
    └── icons/            # 图标资源
```### 1.2 模块依赖关系

```
页面组件 (app/*)
    ↓
业务组件 (components/*)
    ↓
业务逻辑 (lib/actions/*)
    ↓
数据服务 (lib/services/*)
    ↓
数据模型 (prisma/*)
```

---

## 2. API接口文档

### 2.1 产品管理API

**获取产品列表**
```
GET /api/products
```

**请求参数**:
```typescript
interface ProductListParams {
  page?: number          // 页码，默认1
  limit?: number         // 每页数量，默认20
  category?: string      // 产品分类
  status?: string        // 产品状态
  search?: string        // 搜索关键词
}
```

**响应格式**:
```typescript
interface ProductListResponse {
  success: boolean
  data: {
    products: Product[]
    total: number
    page: number
    limit: number
  }
  message: string
}
```

**创建产品**
```
POST /api/products
```

**请求体**:
```typescript
interface CreateProductRequest {
  name: string           // 产品名称
  price: number          // 产品价格
  categoryId: number     // 分类ID
  description?: string   // 产品描述
  sku?: string          // 产品SKU
  material?: string     // 材料
  unit?: string         // 单位
  imageUrl?: string     // 图片URL
}
```### 2.2 订单管理API

**获取订单列表**
```
GET /api/orders
```

**请求参数**:
```typescript
interface OrderListParams {
  page?: number          // 页码
  limit?: number         // 每页数量
  status?: string        // 订单状态
  customerId?: number    // 客户ID
  startDate?: string     // 开始日期
  endDate?: string       // 结束日期
}
```

**创建订单**
```
POST /api/orders
```

**请求体**:
```typescript
interface CreateOrderRequest {
  customerId: number     // 客户ID
  employeeId: number     // 员工ID
  items: OrderItem[]     // 订单项目
  notes?: string         // 订单备注
  expectedDeliveryDate?: string // 预期交货日期
}

interface OrderItem {
  productId: number      // 产品ID
  quantity: number       // 数量
  price: number          // 单价
}
```

### 2.3 库存管理API

**获取库存列表**
```
GET /api/inventory
```

**库存变动记录**
```
POST /api/inventory/transactions
```

**请求体**:
```typescript
interface InventoryTransactionRequest {
  productId: number      // 产品ID
  type: 'IN' | 'OUT' | 'ADJUST'  // 事务类型
  quantity: number       // 数量
  reason: string         // 变动原因
  notes?: string         // 备注
}
```---

## 3. 数据模型说明

### 3.1 核心数据模型

**用户模型 (User)**
```typescript
interface User {
  id: string             // 用户ID (CUID)
  name?: string          // 用户姓名
  email?: string         // 邮箱地址
  password?: string      // 密码哈希
  role: string           // 用户角色
  employeeId?: number    // 关联员工ID
  createdAt: Date        // 创建时间
  updatedAt: Date        // 更新时间
}
```

**产品模型 (Product)**
```typescript
interface Product {
  id: number             // 产品ID
  name: string           // 产品名称
  price?: number         // 产品价格
  cost?: number          // 产品成本
  sku?: string           // 产品SKU
  barcode?: string       // 条形码
  description?: string   // 产品描述
  categoryId?: number    // 分类ID
  material?: string      // 材料
  unit?: string          // 单位
  imageUrl?: string      // 图片URL
  status: string         // 产品状态
  createdAt: Date        // 创建时间
  updatedAt: Date        // 更新时间
}
```

**订单模型 (Order)**
```typescript
interface Order {
  id: number             // 订单ID
  orderNumber: string    // 订单编号
  customerId: number     // 客户ID
  employeeId: number     // 员工ID
  orderDate: Date        // 订单日期
  status: string         // 订单状态
  totalAmount: number    // 订单总金额
  paidAmount: number     // 已付金额
  paymentStatus: string  // 支付状态
  notes?: string         // 订单备注
  createdAt: Date        // 创建时间
  updatedAt: Date        // 更新时间
}
```### 3.2 关系模型

**订单项目模型 (OrderItem)**
```typescript
interface OrderItem {
  id: number             // 订单项ID
  orderId: number        // 订单ID
  productId: number      // 产品ID
  quantity: number       // 数量
  price: number          // 单价
  createdAt: Date        // 创建时间
  updatedAt: Date        // 更新时间
}
```

**库存事务模型 (InventoryTransaction)**
```typescript
interface InventoryTransaction {
  id: number             // 事务ID
  productId: number      // 产品ID
  type: string           // 事务类型 (IN/OUT/ADJUST)
  quantity: number       // 数量
  reason: string         // 变动原因
  referenceId?: string   // 关联单据ID
  notes?: string         // 备注
  createdAt: Date        // 创建时间
}
```

---

## 4. 前端组件库

### 4.1 基础UI组件

**按钮组件 (Button)**
```typescript
interface ButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  asChild?: boolean
  children: React.ReactNode
  onClick?: () => void
  disabled?: boolean
}

// 使用示例
<Button variant="default" size="lg" onClick={handleClick}>
  保存
</Button>
```

**输入框组件 (Input)**
```typescript
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  className?: string
}

// 使用示例
<Input 
  type="text" 
  placeholder="请输入产品名称" 
  value={value}
  onChange={handleChange}
/>
```### 4.2 业务组件

**产品表单组件 (ProductForm)**
```typescript
interface ProductFormProps {
  product?: Product      // 编辑时传入产品数据
  categories: ProductCategory[]  // 产品分类列表
  onSubmit: (data: ProductFormData) => void
  onCancel: () => void
}

// 使用示例
<ProductForm 
  product={selectedProduct}
  categories={categories}
  onSubmit={handleSubmit}
  onCancel={handleCancel}
/>
```

**订单列表组件 (OrderList)**
```typescript
interface OrderListProps {
  orders: Order[]        // 订单列表
  loading?: boolean      // 加载状态
  onEdit: (order: Order) => void
  onDelete: (orderId: number) => void
  onStatusChange: (orderId: number, status: string) => void
}
```

**数据表格组件 (DataTable)**
```typescript
interface DataTableProps<T> {
  data: T[]              // 表格数据
  columns: ColumnDef<T>[] // 列定义
  pagination?: boolean   // 是否分页
  sorting?: boolean      // 是否排序
  filtering?: boolean    // 是否筛选
  onRowClick?: (row: T) => void
}
```

### 4.3 表单组件

**表单验证Hook (useForm)**
```typescript
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'

// 定义验证模式
const productSchema = z.object({
  name: z.string().min(1, '产品名称不能为空'),
  price: z.number().min(0, '价格不能为负数'),
  categoryId: z.number().min(1, '请选择产品分类')
})

// 使用示例
const form = useForm<ProductFormData>({
  resolver: zodResolver(productSchema),
  defaultValues: {
    name: '',
    price: 0,
    categoryId: 0
  }
})
```---

## 5. 业务逻辑层

### 5.1 服务端操作 (Server Actions)

**产品操作 (lib/actions/product-actions.ts)**
```typescript
// 获取产品列表
export async function getProducts(params: ProductListParams): Promise<ProductListResponse> {
  const { page = 1, limit = 20, category, status, search } = params
  
  const where = {
    ...(category && { categoryId: parseInt(category) }),
    ...(status && { status }),
    ...(search && { 
      OR: [
        { name: { contains: search, mode: 'insensitive' } },
        { sku: { contains: search, mode: 'insensitive' } }
      ]
    })
  }
  
  const [products, total] = await Promise.all([
    prisma.product.findMany({
      where,
      include: { productCategory: true },
      skip: (page - 1) * limit,
      take: limit,
      orderBy: { createdAt: 'desc' }
    }),
    prisma.product.count({ where })
  ])
  
  return { products, total, page, limit }
}

// 创建产品
export async function createProduct(data: CreateProductRequest): Promise<Product> {
  return await prisma.product.create({
    data: {
      ...data,
      status: 'active'
    }
  })
}
```

**订单操作 (lib/actions/order-actions.ts)**
```typescript
// 创建订单
export async function createOrder(data: CreateOrderRequest): Promise<Order> {
  return await prisma.$transaction(async (tx) => {
    // 生成订单编号
    const orderNumber = generateOrderNumber()
    
    // 计算订单总金额
    const totalAmount = data.items.reduce((sum, item) => 
      sum + (item.quantity * item.price), 0
    )
    
    // 创建订单
    const order = await tx.order.create({
      data: {
        orderNumber,
        customerId: data.customerId,
        employeeId: data.employeeId,
        orderDate: new Date(),
        status: 'pending',
        totalAmount,
        paidAmount: 0,
        paymentStatus: 'unpaid',
        notes: data.notes
      }
    })
    
    // 创建订单项目
    await tx.orderItem.createMany({
      data: data.items.map(item => ({
        orderId: order.id,
        productId: item.productId,
        quantity: item.quantity,
        price: item.price
      }))
    })
    
    return order
  })
}
```### 5.2 业务服务 (lib/services/)

**权限服务 (permission-service.ts)**
```typescript
export class PermissionService {
  // 检查用户权限
  static async checkUserPermission(userId: string, permissionCode: string): Promise<boolean> {
    const userRoles = await prisma.userRole.findMany({
      where: { userId },
      include: {
        role: {
          include: {
            rolePermissions: {
              include: { permission: true }
            }
          }
        }
      }
    })
    
    return userRoles.some(userRole =>
      userRole.role.rolePermissions.some(rp =>
        rp.permission.code === permissionCode
      )
    )
  }
  
  // 获取用户权限列表
  static async getUserPermissions(userId: string): Promise<string[]> {
    const userRoles = await prisma.userRole.findMany({
      where: { userId },
      include: {
        role: {
          include: {
            rolePermissions: {
              include: { permission: true }
            }
          }
        }
      }
    })
    
    const permissions = new Set<string>()
    userRoles.forEach(userRole => {
      userRole.role.rolePermissions.forEach(rp => {
        permissions.add(rp.permission.code)
      })
    })
    
    return Array.from(permissions)
  }
}
```

---

## 6. 工具函数库

### 6.1 通用工具函数 (lib/utils/)

**日期工具 (date-utils.ts)**
```typescript
import { format, parseISO, isValid } from 'date-fns'
import { zhCN } from 'date-fns/locale'

export class DateUtils {
  // 格式化日期
  static formatDate(date: Date | string, pattern: string = 'yyyy-MM-dd'): string {
    const dateObj = typeof date === 'string' ? parseISO(date) : date
    return isValid(dateObj) ? format(dateObj, pattern, { locale: zhCN }) : ''
  }
  
  // 格式化日期时间
  static formatDateTime(date: Date | string): string {
    return this.formatDate(date, 'yyyy-MM-dd HH:mm:ss')
  }
  
  // 获取日期范围
  static getDateRange(type: 'today' | 'week' | 'month' | 'year'): [Date, Date] {
    const now = new Date()
    const start = new Date(now)
    const end = new Date(now)
    
    switch (type) {
      case 'today':
        start.setHours(0, 0, 0, 0)
        end.setHours(23, 59, 59, 999)
        break
      case 'week':
        start.setDate(now.getDate() - now.getDay())
        start.setHours(0, 0, 0, 0)
        end.setDate(start.getDate() + 6)
        end.setHours(23, 59, 59, 999)
        break
      // ... 其他情况
    }
    
    return [start, end]
  }
}
```**数据验证工具 (validation-utils.ts)**
```typescript
import { z } from 'zod'

export class ValidationUtils {
  // 产品验证模式
  static productSchema = z.object({
    name: z.string().min(1, '产品名称不能为空').max(100, '产品名称不能超过100字符'),
    price: z.number().min(0, '价格不能为负数').optional(),
    categoryId: z.number().min(1, '请选择产品分类'),
    sku: z.string().max(50, 'SKU不能超过50字符').optional(),
    description: z.string().max(1000, '描述不能超过1000字符').optional()
  })
  
  // 订单验证模式
  static orderSchema = z.object({
    customerId: z.number().min(1, '请选择客户'),
    employeeId: z.number().min(1, '请选择员工'),
    items: z.array(z.object({
      productId: z.number().min(1, '请选择产品'),
      quantity: z.number().min(1, '数量必须大于0'),
      price: z.number().min(0, '价格不能为负数')
    })).min(1, '订单必须包含至少一个产品')
  })
  
  // 验证数据
  static validate<T>(schema: z.ZodSchema<T>, data: unknown): { success: boolean; data?: T; errors?: string[] } {
    try {
      const result = schema.parse(data)
      return { success: true, data: result }
    } catch (error) {
      if (error instanceof z.ZodError) {
        return { 
          success: false, 
          errors: error.errors.map(e => e.message) 
        }
      }
      return { success: false, errors: ['验证失败'] }
    }
  }
}
```

**数字工具 (number-utils.ts)**
```typescript
export class NumberUtils {
  // 格式化金额
  static formatCurrency(amount: number, currency: string = '¥'): string {
    return `${currency}${amount.toLocaleString('zh-CN', { 
      minimumFractionDigits: 2,
      maximumFractionDigits: 2 
    })}`
  }
  
  // 格式化百分比
  static formatPercentage(value: number, decimals: number = 2): string {
    return `${(value * 100).toFixed(decimals)}%`
  }
  
  // 安全的数字运算
  static safeAdd(a: number, b: number): number {
    return Math.round((a + b) * 100) / 100
  }
  
  static safeSubtract(a: number, b: number): number {
    return Math.round((a - b) * 100) / 100
  }
  
  static safeMultiply(a: number, b: number): number {
    return Math.round(a * b * 100) / 100
  }
}
```

---

## 7. 中间件系统

### 7.1 认证中间件 (lib/auth-middleware.ts)

```typescript
import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'

export async function withAuth(req: NextRequest) {
  const token = await getToken({
    req,
    secret: process.env.NEXTAUTH_SECRET
  })
  
  if (!token) {
    return NextResponse.json({ error: '未授权' }, { status: 401 })
  }
  
  return undefined // 继续处理请求
}
```### 7.2 权限中间件

```typescript
export async function withPermission(req: NextRequest, permissionCode: string) {
  const authResult = await withAuth(req)
  if (authResult) return authResult
  
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET })
  const hasPermission = await PermissionService.checkUserPermission(
    token!.id as string, 
    permissionCode
  )
  
  if (!hasPermission) {
    return NextResponse.json({ error: '没有权限执行此操作' }, { status: 403 })
  }
  
  return undefined
}
```

---

## 8. 配置管理

### 8.1 环境配置

**开发环境 (.env.local)**
```bash
# 数据库配置
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/linghua_enamel_gallery"

# 认证配置
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-key"

# 应用配置
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_APP_NAME="聆花掐丝珐琅馆管理系统"
```

**生产环境 (.env.production)**
```bash
# 数据库配置
DATABASE_URL="************************************/database"

# 认证配置
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="your-production-secret"

# 应用配置
NEXT_PUBLIC_APP_URL="https://your-domain.com"
```

### 8.2 应用配置 (lib/config.ts)

```typescript
export const config = {
  app: {
    name: process.env.NEXT_PUBLIC_APP_NAME || '聆花珐琅馆ERP',
    url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0'
  },
  
  database: {
    url: process.env.DATABASE_URL!
  },
  
  auth: {
    secret: process.env.NEXTAUTH_SECRET!,
    url: process.env.NEXTAUTH_URL!
  },
  
  pagination: {
    defaultPageSize: 20,
    maxPageSize: 100
  },
  
  upload: {
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif']
  }
}
```

---

## 9. 测试规范

### 9.1 单元测试

**组件测试示例**
```typescript
import { render, screen, fireEvent } from '@testing-library/react'
import { ProductForm } from '@/components/product-form'

describe('ProductForm', () => {
  const mockCategories = [
    { id: 1, name: '珐琅饰品' },
    { id: 2, name: '珐琅摆件' }
  ]
  
  it('应该正确渲染表单', () => {
    render(
      <ProductForm 
        categories={mockCategories}
        onSubmit={jest.fn()}
        onCancel={jest.fn()}
      />
    )
    
    expect(screen.getByLabelText('产品名称')).toBeInTheDocument()
    expect(screen.getByLabelText('产品价格')).toBeInTheDocument()
    expect(screen.getByLabelText('产品分类')).toBeInTheDocument()
  })
  
  it('应该验证必填字段', async () => {
    const mockSubmit = jest.fn()
    render(
      <ProductForm 
        categories={mockCategories}
        onSubmit={mockSubmit}
        onCancel={jest.fn()}
      />
    )
    
    fireEvent.click(screen.getByText('保存'))
    
    expect(await screen.findByText('产品名称不能为空')).toBeInTheDocument()
    expect(mockSubmit).not.toHaveBeenCalled()
  })
})
```### 9.2 API测试

**API测试示例**
```typescript
import { createMocks } from 'node-mocks-http'
import handler from '@/app/api/products/route'

describe('/api/products', () => {
  it('GET - 应该返回产品列表', async () => {
    const { req, res } = createMocks({
      method: 'GET',
      query: { page: '1', limit: '10' }
    })
    
    await handler(req, res)
    
    expect(res._getStatusCode()).toBe(200)
    
    const data = JSON.parse(res._getData())
    expect(data.success).toBe(true)
    expect(Array.isArray(data.data.products)).toBe(true)
  })
  
  it('POST - 应该创建新产品', async () => {
    const productData = {
      name: '测试产品',
      price: 100,
      categoryId: 1
    }
    
    const { req, res } = createMocks({
      method: 'POST',
      body: productData
    })
    
    await handler(req, res)
    
    expect(res._getStatusCode()).toBe(201)
    
    const data = JSON.parse(res._getData())
    expect(data.success).toBe(true)
    expect(data.data.name).toBe(productData.name)
  })
})
```

### 9.3 E2E测试

**Playwright测试示例**
```typescript
import { test, expect } from '@playwright/test'

test.describe('产品管理', () => {
  test('应该能够创建新产品', async ({ page }) => {
    // 登录
    await page.goto('/login')
    await page.fill('[name="email"]', '<EMAIL>')
    await page.fill('[name="password"]', 'password')
    await page.click('button[type="submit"]')
    
    // 导航到产品管理
    await page.goto('/products')
    await expect(page.locator('h1')).toContainText('产品管理')
    
    // 创建新产品
    await page.click('text=新增产品')
    await page.fill('[name="name"]', '测试产品')
    await page.fill('[name="price"]', '100')
    await page.selectOption('[name="categoryId"]', '1')
    await page.click('text=保存')
    
    // 验证产品已创建
    await expect(page.locator('text=测试产品')).toBeVisible()
  })
})
```

---

## 10. 开发指南

### 10.1 开发环境设置

**环境要求**:
- Node.js 18+
- PostgreSQL 15+
- Docker (可选)

**安装步骤**:
```bash
# 1. 克隆项目
git clone <repository-url>
cd linghua-enamel-gallery

# 2. 安装依赖
npm install

# 3. 配置环境变量
cp .env.example .env.local
# 编辑 .env.local 文件

# 4. 初始化数据库
npx prisma migrate dev
npx prisma db seed

# 5. 启动开发服务器
npm run dev
```

### 10.2 代码规范

**命名规范**:
- 文件名: kebab-case (product-form.tsx)
- 组件名: PascalCase (ProductForm)
- 函数名: camelCase (createProduct)
- 常量名: UPPER_SNAKE_CASE (MAX_FILE_SIZE)

**目录结构规范**:
- 页面文件放在 `app/` 目录
- 组件文件放在 `components/` 目录
- 工具函数放在 `lib/utils/` 目录
- 业务逻辑放在 `lib/actions/` 目录

**代码提交规范**:
```bash
# 提交格式
<type>(<scope>): <description>

# 示例
feat(products): 添加产品批量导入功能
fix(orders): 修复订单状态更新问题
docs(api): 更新API文档
```

### 10.3 部署指南

**Docker部署**:
```bash
# 构建镜像
docker build -t linghua-erp .

# 运行容器
docker run -p 3000:3000 \
  -e DATABASE_URL="your-database-url" \
  -e NEXTAUTH_SECRET="your-secret" \
  linghua-erp
```

**生产环境检查清单**:
- [ ] 环境变量配置正确
- [ ] 数据库迁移已执行
- [ ] SSL证书已配置
- [ ] 备份策略已设置
- [ ] 监控告警已配置

---

**文档维护说明**:
- 新增模块需要更新相应文档
- API变更需要同步更新接口文档
- 重要功能变更需要更新开发指南
- 定期审查和优化代码结构