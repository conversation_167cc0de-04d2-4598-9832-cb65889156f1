# Database Configuration - Docker PostgreSQL
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/linghua_enamel_gallery?schema=public"

# NextAuth Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-key-here-make-it-very-long-and-random-for-production"

# Application Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_APP_NAME="聆花掐丝珐琅馆管理系统"
NEXT_PUBLIC_APP_VERSION="1.0.0"

# Environment
NODE_ENV="development"

# NOTE: Please replace the database credentials with your actual values:
# - Replace 'username' with your PostgreSQL username
# - Replace 'password' with your PostgreSQL password
# - Replace 'localhost:5432' with your database host and port
# - Replace 'linghua' with your actual database name
