import { NextResponse } from "next/server"
import prisma from "@/lib/db"
import { ErrorUtils } from "@/lib/error-utils"
import { ZodValidationUtils } from "@/lib/zod-validation-utils"
import { EmployeeSchemas } from "@/lib/zod-schemas"

export async function GET() {
  try {
    const employees = await prisma.employee.findMany({
      orderBy: {
        id: "asc",
      },
    })

    return NextResponse.json(employees)
  } catch (error) {
    const appError = await ErrorUtils.handleError(error, 'employees-api');
    const errorResponse = ErrorUtils.formatErrorResponse(appError);

    return NextResponse.json(errorResponse, {
      status: appError.type === ErrorUtils.ErrorType.VALIDATION ? 400 : 500
    });
  }
}

export async function POST(request: Request) {
  try {
    const rawData = await request.json()

    // 使用Zod进行输入验证
    let validatedData;
    try {
      validatedData = ZodValidationUtils.validateOrThrow(
        EmployeeSchemas.create,
        rawData,
        'employees-api-zod'
      );
    } catch (validationError) {
      if (validationError instanceof ErrorUtils.ValidationError) {
        const errorResponse = ErrorUtils.formatErrorResponse(validationError);
        return NextResponse.json(errorResponse, { status: 400 });
      }
      throw validationError;
    }

    const employee = await prisma.employee.create({
      data: {
        name: validatedData.name,
        position: validatedData.position,
        phone: validatedData.phone,
        email: validatedData.email,
        hireDate: validatedData.hireDate ? new Date(validatedData.hireDate) : new Date(),
        salary: validatedData.salary || 0,
        commissionRate: validatedData.commissionRate || 0,
        isActive: validatedData.isActive !== undefined ? validatedData.isActive : true,
      },
    })

    return NextResponse.json(employee)
  } catch (error) {
    const appError = await ErrorUtils.handleError(error, 'employees-api-create');
    const errorResponse = ErrorUtils.formatErrorResponse(appError);

    return NextResponse.json(errorResponse, { status: 500 });
  }
}
