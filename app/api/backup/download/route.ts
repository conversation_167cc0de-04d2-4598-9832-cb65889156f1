import { NextResponse } from "next/server"
import { getServerSession } from "@/lib/auth-helpers"
import fs from "fs"
import path from "path"

// 备份目录
const BACKUP_DIR = path.join(process.cwd(), "backups")

export async function GET(request: Request) {
  try {
    // 检查用户是否已登录且有权限
    if (!(await isAdmin())) {
      return NextResponse.json({ error: "未授权" }, { status: 403 })
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url)
    const filename = searchParams.get("filename")

    if (!filename) {
      return NextResponse.json({ error: "缺少文件名参数" }, { status: 400 })
    }

    // 验证文件名，防止路径遍历攻击
    if (filename.includes("..") || filename.includes("/") || filename.includes("\\")) {
      return NextResponse.json({ error: "无效的文件名" }, { status: 400 })
    }

    // 构建文件路径
    const filePath = path.join(BACKUP_DIR, filename)

    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return NextResponse.json({ error: "文件不存在" }, { status: 404 })
    }

    // 读取文件内容
    const fileContent = fs.readFileSync(filePath)

    // 返回文件内容
    return new NextResponse(fileContent, {
      headers: {
        "Content-Type": "application/json",
        "Content-Disposition": `attachment; filename="${filename}"`,
      },
    })
  } catch (error) {
    console.error("Error downloading backup:", error)
    return NextResponse.json(
      { error: "Failed to download backup" },
      { status: 500 }
    )
  }
}
