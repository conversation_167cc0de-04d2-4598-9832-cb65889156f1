/**
 * 订单-库存同步原子性测试脚本
 * 
 * 此脚本用于测试修复后的订单创建功能，验证订单创建和库存扣减的原子性
 */

import { createSalesOrder } from './lib/actions/sales-actions.js';

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 测试基本订单创建功能
 */
async function testBasicOrderCreation() {
  console.log('\n=== 测试基本订单创建功能 ===');
  
  try {
    const orderData = {
      customerId: 1,
      employeeId: 1,
      orderDate: new Date(),
      status: 'pending',
      paymentStatus: 'unpaid',
      items: [
        { productId: 1, quantity: 2, price: 100 },
        { productId: 2, quantity: 1, price: 200 }
      ]
    };
    
    const result = await createSalesOrder(orderData);
    console.log('✅ 基本订单创建测试成功:', { orderId: result.id, totalAmount: result.totalAmount });
  } catch (error) {
    console.error('❌ 基本订单创建测试失败:', error.message);
  }
}

/**
 * 测试并发订单创建
 */
async function testConcurrentOrderCreation() {
  console.log('\n=== 测试并发订单创建 ===');
  
  const concurrentOrders = [];
  
  // 创建5个并发订单操作，使用相同的产品
  for (let i = 0; i < 5; i++) {
    const orderData = {
      customerId: 1,
      employeeId: 1,
      orderDate: new Date(),
      status: 'pending',
      paymentStatus: 'unpaid',
      items: [
        { productId: 1, quantity: 3, price: 100 },
        { productId: 2, quantity: 1, price: 200 }
      ],
      notes: `并发订单测试 #${i + 1}`
    };
    
    concurrentOrders.push(
      createSalesOrder(orderData).catch(error => ({
        error: true,
        message: error.message,
        index: i + 1
      }))
    );
  }
  
  try {
    const results = await Promise.all(concurrentOrders);
    
    const successes = results.filter(r => !r.error);
    const failures = results.filter(r => r.error);
    
    console.log(`✅ 成功的订单创建: ${successes.length}`);
    console.log(`❌ 失败的订单创建: ${failures.length}`);
    
    failures.forEach(failure => {
      console.log(`   - 订单 #${failure.index}: ${failure.message}`);
    });
    
  } catch (error) {
    console.error('❌ 并发订单创建测试失败:', error.message);
  }
}

/**
 * 测试库存不足的情况
 */
async function testInsufficientInventoryOrder() {
  console.log('\n=== 测试库存不足情况 ===');
  
  try {
    const orderData = {
      customerId: 1,
      employeeId: 1,
      orderDate: new Date(),
      status: 'pending',
      paymentStatus: 'unpaid',
      items: [
        { productId: 1, quantity: 999999, price: 100 } // 远超实际库存
      ],
      notes: '库存不足测试'
    };
    
    const result = await createSalesOrder(orderData);
    console.log('❌ 库存不足测试应该失败，但成功了:', result);
  } catch (error) {
    if (error.message.includes('库存不足')) {
      console.log('✅ 库存不足测试成功，正确拒绝了订单:', error.message);
    } else {
      console.error('❌ 库存不足测试失败，错误类型不正确:', error.message);
    }
  }
}

/**
 * 测试无效订单数据
 */
async function testInvalidOrderData() {
  console.log('\n=== 测试无效订单数据 ===');
  
  const testCases = [
    {
      name: '空订单项',
      data: { customerId: 1, employeeId: 1, items: [] }
    },
    {
      name: '无效产品ID',
      data: { 
        customerId: 1, 
        employeeId: 1, 
        items: [{ productId: 99999, quantity: 1, price: 100 }] 
      }
    },
    {
      name: '无效客户ID',
      data: { 
        customerId: 99999, 
        employeeId: 1, 
        items: [{ productId: 1, quantity: 1, price: 100 }] 
      }
    },
    {
      name: '无效员工ID',
      data: { 
        customerId: 1, 
        employeeId: 99999, 
        items: [{ productId: 1, quantity: 1, price: 100 }] 
      }
    },
    {
      name: '负数量',
      data: { 
        customerId: 1, 
        employeeId: 1, 
        items: [{ productId: 1, quantity: -1, price: 100 }] 
      }
    },
    {
      name: '负价格',
      data: { 
        customerId: 1, 
        employeeId: 1, 
        items: [{ productId: 1, quantity: 1, price: -100 }] 
      }
    }
  ];
  
  for (const testCase of testCases) {
    try {
      const result = await createSalesOrder(testCase.data);
      console.log(`❌ ${testCase.name}测试应该失败，但成功了:`, result);
    } catch (error) {
      console.log(`✅ ${testCase.name}测试成功，正确拒绝了请求:`, error.message);
    }
  }
}

/**
 * 测试订单回滚机制
 */
async function testOrderRollback() {
  console.log('\n=== 测试订单回滚机制 ===');
  
  try {
    // 创建一个可能导致中途失败的订单（故意触发错误）
    const orderData = {
      customerId: 1,
      employeeId: 1,
      orderDate: new Date(),
      status: 'pending',
      paymentStatus: 'unpaid',
      items: [
        { productId: 1, quantity: 1, price: 100 },
        { productId: 999, quantity: 1, price: 200 } // 不存在的产品
      ],
      notes: '回滚测试'
    };
    
    await createSalesOrder(orderData);
    console.log('❌ 回滚测试应该失败，但成功了');
  } catch (error) {
    console.log('✅ 回滚测试成功，订单创建失败，应该没有部分数据残留:', error.message);
    
    // 这里可以添加验证，确认没有创建部分订单数据
    // 例如检查数据库中是否有孤立的订单记录
  }
}

/**
 * 测试支付处理
 */
async function testPaymentProcessing() {
  console.log('\n=== 测试支付处理 ===');
  
  const testCases = [
    {
      name: '全额支付',
      paidAmount: 300, // 订单总额为300
      expectedStatus: 'paid'
    },
    {
      name: '部分支付',
      paidAmount: 150,
      expectedStatus: 'partial'
    },
    {
      name: '未支付',
      paidAmount: 0,
      expectedStatus: 'unpaid'
    }
  ];
  
  for (const testCase of testCases) {
    try {
      const orderData = {
        customerId: 1,
        employeeId: 1,
        orderDate: new Date(),
        status: 'pending',
        paymentStatus: 'unpaid',
        paymentMethod: 'cash',
        paidAmount: testCase.paidAmount,
        items: [
          { productId: 1, quantity: 1, price: 100 },
          { productId: 2, quantity: 1, price: 200 }
        ],
        notes: `${testCase.name}测试`
      };
      
      const result = await createSalesOrder(orderData);
      
      if (testCase.paidAmount > 0) {
        // 对于有支付的情况，需要验证支付状态
        console.log(`✅ ${testCase.name}测试: 订单ID ${result.id}`);
      } else {
        console.log(`✅ ${testCase.name}测试: 订单ID ${result.id}`);
      }
    } catch (error) {
      console.error(`❌ ${testCase.name}测试失败:`, error.message);
    }
  }
}

/**
 * 执行性能测试
 */
async function testPerformance() {
  console.log('\n=== 性能测试 ===');
  
  const startTime = Date.now();
  const orders = [];
  
  // 执行20个订单创建操作
  for (let i = 0; i < 20; i++) {
    const orderData = {
      customerId: 1,
      employeeId: 1,
      orderDate: new Date(),
      status: 'pending',
      paymentStatus: 'unpaid',
      items: [
        { productId: 1, quantity: 1, price: 100 }
      ],
      notes: `性能测试 #${i + 1}`
    };
    
    orders.push(
      createSalesOrder(orderData).catch(error => ({ 
        error: true, 
        message: error.message 
      }))
    );
    
    // 间隔20ms避免过度并发
    if (i % 5 === 0) {
      await sleep(20);
    }
  }
  
  try {
    const results = await Promise.all(orders);
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    const successes = results.filter(r => !r.error).length;
    const failures = results.filter(r => r.error).length;
    
    console.log(`⏱️  性能测试结果:`);
    console.log(`   - 总时间: ${duration}ms`);
    console.log(`   - 平均时间: ${(duration / 20).toFixed(2)}ms/订单`);
    console.log(`   - 成功订单: ${successes}`);
    console.log(`   - 失败订单: ${failures}`);
    console.log(`   - 成功率: ${((successes / 20) * 100).toFixed(1)}%`);
    
  } catch (error) {
    console.error('❌ 性能测试失败:', error.message);
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始订单-库存同步原子性测试\n');
  
  await testBasicOrderCreation();
  await sleep(1000);
  
  await testConcurrentOrderCreation();
  await sleep(1000);
  
  await testInsufficientInventoryOrder();
  await sleep(1000);
  
  await testInvalidOrderData();
  await sleep(1000);
  
  await testOrderRollback();
  await sleep(1000);
  
  await testPaymentProcessing();
  await sleep(1000);
  
  await testPerformance();
  
  console.log('\n✅ 所有测试完成！');
  console.log('📝 注意：如果您看到任何❌标记的测试失败，请检查订单-库存同步实现。');
}

// 执行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests().catch(error => {
    console.error('💥 测试执行失败:', error);
    process.exit(1);
  });
}

export {
  testBasicOrderCreation,
  testConcurrentOrderCreation,
  testInsufficientInventoryOrder,
  testInvalidOrderData,
  testOrderRollback,
  testPaymentProcessing,
  testPerformance
};