/**
 * Next.js 性能优化配置 - 解决编译缓慢和运行时性能问题
 * @type {import('next').NextConfig}
 */
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const nextConfig = {
  // 🚀 编译优化
  experimental: {
    // 启用静态优化
    optimizeCss: true,
    // 启用ESM外部化
    esmExternals: true,
    // swcMinify已移除，Next.js 15默认使用SWC
  },

  // 📦 Webpack优化配置
  webpack: (config, { dev, isServer, webpack }) => {
    // 🔧 启用文件系统缓存 - 大幅减少编译时间
    config.cache = {
      type: 'filesystem',
      buildDependencies: {
        config: [__filename],
      },
      // 缓存目录 - 使用绝对路径
      cacheDirectory: `${__dirname}/.next/cache/webpack`,
    };

    // 🎯 生产环境代码分割优化
    if (!isServer) {
      // Fix for ChunkLoadError - Enhanced configuration
      config.output.chunkFilename = dev
        ? 'static/chunks/[name].js'
        : 'static/chunks/[name].[contenthash].js';

      // Improve chunk loading reliability
      config.output.crossOriginLoading = 'anonymous';

      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          minSize: 20000,
          maxSize: 244000,
          cacheGroups: {
            // 第三方库单独打包
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
              priority: 10,
              enforce: true,
            },
            // 公共组件
            common: {
              name: 'common',
              minChunks: 2,
              chunks: 'all',
              priority: 5,
              reuseExistingChunk: true,
            },
            // Prisma客户端单独打包
            prisma: {
              test: /[\\/]node_modules[\\/]@prisma[\\/]/,
              name: 'prisma',
              chunks: 'all',
              priority: 15,
            },
            // Next.js相关包
            framework: {
              test: /[\\/]node_modules[\\/](react|react-dom|next)[\\/]/,
              name: 'framework',
              chunks: 'all',
              priority: 20,
            },
          },
        },
      };
    }

    // 🔍 模块解析优化
    config.resolve = {
      ...config.resolve,
      // 减少模块查找范围
      modules: ['node_modules'],
      // 缓存模块解析
      cache: true,
    };

    // ⚡ 开发环境优化
    if (dev) {
      // 减少监听文件
      config.watchOptions = {
        ignored: /node_modules|\.next/,
        aggregateTimeout: 300,
        poll: false,
      };
    }

    return config;
  },

  // 🗜️ 压缩和优化
  compress: true,
  poweredByHeader: false,
  generateEtags: true,

  // 🖼️ 图片优化配置
  images: {
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 3600,
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    unoptimized: false, // 启用图片优化
  },

  // 📝 TypeScript优化
  typescript: {
    // 开发时保留错误检查，生产构建时忽略以加快构建
    ignoreBuildErrors: process.env.NODE_ENV === 'production',
  },

  // 🔧 ESLint优化
  eslint: {
    // 开发时保留检查，生产构建时忽略以加快构建
    ignoreDuringBuilds: process.env.NODE_ENV === 'production',
  },

  // 🎯 环境变量优化
  env: {
    // 禁用调试模式
    NEXTAUTH_DEBUG: 'false',
    // 设置日志级别
    LOG_LEVEL: process.env.NODE_ENV === 'production' ? 'warn' : 'info',
  },

  // 📋 HTTP头优化
  async headers() {
    return [
      {
        // 静态资源缓存
        source: '/uploads/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      {
        // API路由缓存
        source: '/api/products',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=300, s-maxage=300',
          },
        ],
      },
      {
        // 分类API缓存
        source: '/api/product-categories',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=600, s-maxage=600',
          },
        ],
      },
    ];
  },

  // Add retry configuration for chunk loading
  onDemandEntries: {
    maxInactiveAge: 25 * 1000,
    pagesBufferLength: 2,
  },

  // Enable standalone output for Docker production builds
  output: 'standalone',
}

export default nextConfig
