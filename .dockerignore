# 依赖
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js
.next/
out/

# 生产构建
build

# 环境变量
.env*.local
.env.production

# 调试
.vscode/
.idea/

# 操作系统
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# 测试
coverage/
.nyc_output

# 日志
logs
*.log

# 临时文件
tmp/
temp/

# 备份文件
*.backup
*.bak
*.tmp

# 编辑器
*.swp
*.swo
*~

# 其他
README.md
LICENSE
docs/